import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import GoogleProvider from 'next-auth/providers/google'
import { prisma } from './prisma'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  debug: process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_DEBUG_MODE === 'true',
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          // Temporarily using basic scopes only to avoid verification requirement
          // Original Gmail scopes (requires Google app verification):
          // scope: "openid email profile https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.compose https://www.googleapis.com/auth/gmail.modify",
          scope: "openid email profile",
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async session({ session, token }) {
      if (session?.user && token?.sub) {
        session.user.id = token.sub
        session.accessToken = token.accessToken
        session.refreshToken = token.refreshToken
      }
      return session
    },
    async jwt({ token, user, account }) {
      if (user) {
        token.sub = user.id
      }
      if (account) {
        token.accessToken = account.access_token
        token.refreshToken = account.refresh_token
        token.expiresAt = account.expires_at
      }
      return token
    },
    async signIn() {
      return true // Let the adapter handle user creation
    }
  },
  events: {
    async createUser({ user }) {
      try {
        // Check if profile data already exists (to avoid duplicates)
        const existingSettings = await prisma.userSettings.findUnique({
          where: { userId: user.id }
        })
        
        if (!existingSettings) {
          // This runs after the adapter creates the user
          // Now we add the additional profile data
          await prisma.user.update({
            where: { id: user.id },
            data: {
              firstName: user.name?.split(' ')[0] || '',
              lastName: user.name?.split(' ').slice(1).join(' ') || '',
              settings: {
                create: {
                  // Default settings will be applied from schema
                }
              },
              subscription: {
                create: {
                  plan: 'free',
                  status: 'active'
                }
              },
              quotas: {
                create: [
                  {
                    quotaType: 'blog_posts',
                    totalLimit: 5, // Free tier: 5 blog posts per month
                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
                  },
                  {
                    quotaType: 'emails',
                    totalLimit: 10, // Free tier: 10 emails per month
                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
                  },
                  {
                    quotaType: 'social_media',
                    totalLimit: 20, // Free tier: 20 social media posts per month
                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
                  },
                  {
                    quotaType: 'youtube_scripts',
                    totalLimit: 3, // Free tier: 3 YouTube scripts per month
                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
                  },
                  {
                    quotaType: 'invincible_research',
                    totalLimit: 2, // Free tier: 2 research queries per month
                    resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
                  }
                ]
              }
            }
          })
          console.log(`✅ Created complete user profile for: ${user.email}`)
        } else {
          console.log(`ℹ️ User profile already exists for: ${user.email}`)
        }
      } catch (error) {
        console.error('Error setting up user profile:', error)
      }
    }
  },
  pages: {
    signIn: '/login',
    error: '/login?error=auth_error',
  }
}