/**
 * Ideogram API Service
 * Handles image generation using Ideogram's V_2A_TURBO model
 */

export interface IdeogramImageRequest {
  prompt: string
  aspect_ratio?: 'ASPECT_10_16' | 'ASPECT_16_10' | 'ASPECT_1_1' | 'ASPECT_9_16' | 'ASPECT_16_9'
  model?: 'V_2A_TURBO' | 'V_2A' | 'V_2' | 'V_1_TURBO' | 'V_1'
  magic_prompt_option?: 'AUTO' | 'ON' | 'OFF'
  seed?: number
  style_type?: 'AUTO' | 'GENERAL' | 'REALISTIC' | 'DESIGN' | 'RENDER_3D' | 'ANIME'
}

export interface IdeogramImageResponse {
  created: string
  data: Array<{
    prompt: string
    resolution: string
    is_image_safe: boolean
    seed: number
    url: string
    style_type: string
  }>
}

export class IdeogramService {
  private apiKey: string
  private baseUrl = 'https://api.ideogram.ai'

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.IDEOGRAM_API_KEY || ''
    // Only warn if no API key is provided at all
    if (!this.apiKey && !apiKey) {
      console.warn('⚠️ Ideogram API key not configured')
    }
  }

  /**
   * Generate an image using Ideogram API
   */
  async generateImage(request: IdeogramImageRequest): Promise<IdeogramImageResponse> {
    if (!this.apiKey) {
      throw new Error('Ideogram API key not configured')
    }

    try {
      console.log(`🎨 Generating image with Ideogram: "${request.prompt.substring(0, 50)}..."`)

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_request: {
            prompt: request.prompt,
            aspect_ratio: request.aspect_ratio || 'ASPECT_16_9',
            model: request.model || 'V_2A_TURBO',
            magic_prompt_option: request.magic_prompt_option || 'AUTO',
            style_type: request.style_type || 'AUTO',
            ...(request.seed && { seed: request.seed })
          }
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Ideogram API error ${response.status}: ${errorText}`)
      }

      const result: IdeogramImageResponse = await response.json()
      
      if (!result.data || result.data.length === 0) {
        throw new Error('No images generated')
      }

      console.log(`✅ Image generated successfully: ${result.data[0].url}`)
      return result

    } catch (error) {
      console.error('❌ Ideogram image generation failed:', error)
      throw error
    }
  }

  /**
   * Generate an image optimized for blog headings using Gemini-enhanced prompts
   */
  async generateBlogHeadingImage(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    const enhancedPrompt = await this.createEnhancedBlogImagePrompt(headingText, context, blogTitle)

    const result = await this.generateImage({
      prompt: enhancedPrompt,
      aspect_ratio: 'ASPECT_16_9',
      model: 'V_2A_TURBO',
      magic_prompt_option: 'ON', // Enable magic prompt for better results
      style_type: 'DESIGN' // Better for typography and professional designs
    })

    return result.data[0].url
  }

  /**
   * Create an enhanced prompt using Gemini for better image generation
   */
  private async createEnhancedBlogImagePrompt(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    try {
      // Use Gemini to create a better prompt
      const geminiPrompt = await this.generatePromptWithGemini(headingText, context, blogTitle)

      // Combine with typography-focused instructions
      const enhancedPrompt = `${geminiPrompt}

TYPOGRAPHY & DESIGN REQUIREMENTS:
- MUST include the exact text "${headingText}" prominently displayed
- Use elegant, modern typography (Helvetica, Montserrat, or similar sans-serif)
- Text should be large, bold, and highly readable
- High contrast between text and background (dark text on light background or vice versa)
- Typography is the PRIMARY focal point of the design
- Clean, minimalist layout with generous white space around text
- Professional color palette: blues, grays, whites with one vibrant accent color
- Crisp, sharp text rendering with perfect letter spacing
- Background should complement, not compete with the typography
- Subtle geometric shapes or gradients as background elements
- 16:9 aspect ratio, horizontal layout optimized for blog headers
- Modern, professional design suitable for business/tech content
- Avoid cluttered or busy backgrounds that distract from text
- Text should be centered or well-positioned for maximum impact`

      return enhancedPrompt
    } catch (error) {
      console.warn('Failed to enhance prompt with Gemini, using fallback:', error)
      return this.createFallbackBlogImagePrompt(headingText, context)
    }
  }

  /**
   * Generate enhanced prompt using Gemini
   */
  private async generatePromptWithGemini(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    const geminiPromptRequest = `Create a detailed, creative prompt for generating a professional blog header image with TYPOGRAPHY as the main focus.

HEADING TEXT TO DISPLAY: "${headingText}"
${blogTitle ? `BLOG TITLE: "${blogTitle}"` : ''}
${context ? `CONTENT CONTEXT: ${context.substring(0, 300)}` : ''}

Create a prompt for a typography-focused header image that will:

1. **Typography Focus**: The heading text "${headingText}" must be prominently displayed with beautiful, readable typography
2. **Visual Concept**: Describe visual metaphors, symbols, or abstract elements that represent the topic
3. **Design Style**: Modern, professional, minimalist design with clean layouts
4. **Color Palette**: Suggest specific colors that match the topic's mood (blues for tech, greens for growth, etc.)
5. **Background Elements**: Subtle geometric shapes, gradients, or textures that enhance but don't compete with text
6. **Professional Appeal**: Suitable for business/professional blog content

Requirements:
- Text must be the focal point
- Clean, uncluttered design
- High contrast for readability
- 16:9 aspect ratio
- Modern typography style

Return only the detailed image generation prompt, no explanations or additional text.`

    try {
      // Check if we're in a server environment
      if (typeof window !== 'undefined') {
        // Client-side: make API call
        const response = await fetch('/api/gemini-prompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: geminiPromptRequest
          })
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP ${response.status}`)
        }

        const data = await response.json()
        return data.prompt || this.createFallbackBlogImagePrompt(headingText, context)
      } else {
        // Server-side: use Gemini service directly
        const { GeminiService } = await import('./gemini')
        const gemini = new GeminiService()

        const result = await gemini.generateContent(geminiPromptRequest, {
          temperature: 0.8,
          maxOutputTokens: 1000,
          thinkingConfig: {
            thinkingBudget: 0,
            includeThoughts: false
          }
        })

        return result.response.trim() || this.createFallbackBlogImagePrompt(headingText, context)
      }
    } catch (error) {
      console.warn('Gemini prompt generation failed, using fallback:', error instanceof Error ? error.message : error)
      return this.createFallbackBlogImagePrompt(headingText, context)
    }
  }

  /**
   * Fallback prompt creation method with typography focus
   */
  private createFallbackBlogImagePrompt(headingText: string, context?: string): string {
    const cleanHeading = headingText.replace(/[#*_`]/g, '').trim()

    let prompt = `Professional typography-focused blog header image featuring the text "${cleanHeading}" in large, bold, modern sans-serif font. `

    // Add context-based visual elements
    if (context) {
      const contextLower = context.toLowerCase()
      if (contextLower.includes('technology') || contextLower.includes('tech') || contextLower.includes('digital')) {
        prompt += `Tech-themed background with subtle circuit patterns or geometric shapes in blue tones. `
      } else if (contextLower.includes('business') || contextLower.includes('strategy') || contextLower.includes('growth')) {
        prompt += `Professional business background with subtle arrow or growth elements in navy and gold tones. `
      } else if (contextLower.includes('design') || contextLower.includes('creative') || contextLower.includes('art')) {
        prompt += `Creative background with subtle artistic elements and vibrant color accents. `
      } else {
        prompt += `Clean, professional background with subtle geometric elements. `
      }
    } else {
      prompt += `Clean, minimalist background with subtle gradient. `
    }

    prompt += `High contrast text, centered layout, 16:9 aspect ratio, modern typography as the focal point, professional color scheme, crisp text rendering, suitable for blog article header.`

    return prompt
  }

  /**
   * Download and convert image to base64 (for storage)
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const base64 = Buffer.from(arrayBuffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'
      
      return `data:${mimeType};base64,${base64}`
    } catch (error) {
      console.error('❌ Failed to download image:', error)
      throw error
    }
  }
}

// Export singleton instance
export const ideogramService = new IdeogramService()
