/**
 * Ideogram API Service
 * Handles image generation using Ideogram's V_2A_TURBO model
 */

export interface IdeogramImageRequest {
  prompt: string
  aspect_ratio?: 'ASPECT_10_16' | 'ASPECT_16_10' | 'ASPECT_1_1' | 'ASPECT_9_16' | 'ASPECT_16_9'
  model?: 'V_2A_TURBO' | 'V_2A' | 'V_2' | 'V_1_TURBO' | 'V_1'
  magic_prompt_option?: 'AUTO' | 'ON' | 'OFF'
  seed?: number
  style_type?: 'AUTO' | 'GENERAL' | 'REALISTIC' | 'DESIGN' | 'RENDER_3D' | 'ANIME'
}

export interface IdeogramImageResponse {
  created: string
  data: Array<{
    prompt: string
    resolution: string
    is_image_safe: boolean
    seed: number
    url: string
    style_type: string
  }>
}

export class IdeogramService {
  private apiKey: string
  private baseUrl = 'https://api.ideogram.ai'

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.IDEOGRAM_API_KEY || ''
    if (!this.apiKey) {
      console.warn('⚠️ Ideogram API key not configured')
    }
  }

  /**
   * Generate an image using Ideogram API
   */
  async generateImage(request: IdeogramImageRequest): Promise<IdeogramImageResponse> {
    if (!this.apiKey) {
      throw new Error('Ideogram API key not configured')
    }

    try {
      console.log(`🎨 Generating image with Ideogram: "${request.prompt.substring(0, 50)}..."`)

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_request: {
            prompt: request.prompt,
            aspect_ratio: request.aspect_ratio || 'ASPECT_16_9',
            model: request.model || 'V_2A_TURBO',
            magic_prompt_option: request.magic_prompt_option || 'AUTO',
            style_type: request.style_type || 'AUTO',
            ...(request.seed && { seed: request.seed })
          }
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Ideogram API error ${response.status}: ${errorText}`)
      }

      const result: IdeogramImageResponse = await response.json()
      
      if (!result.data || result.data.length === 0) {
        throw new Error('No images generated')
      }

      console.log(`✅ Image generated successfully: ${result.data[0].url}`)
      return result

    } catch (error) {
      console.error('❌ Ideogram image generation failed:', error)
      throw error
    }
  }

  /**
   * Generate an image optimized for blog headings
   */
  async generateBlogHeadingImage(headingText: string, context?: string): Promise<string> {
    const prompt = this.createBlogImagePrompt(headingText, context)
    
    const result = await this.generateImage({
      prompt,
      aspect_ratio: 'ASPECT_16_9',
      model: 'V_2A_TURBO',
      magic_prompt_option: 'AUTO',
      style_type: 'GENERAL'
    })

    return result.data[0].url
  }

  /**
   * Create an optimized prompt for blog heading images
   */
  private createBlogImagePrompt(headingText: string, context?: string): string {
    // Clean the heading text
    const cleanHeading = headingText.replace(/[#*_`]/g, '').trim()
    
    // Create a descriptive prompt
    let prompt = `Professional, modern illustration representing "${cleanHeading}". `
    
    if (context) {
      prompt += `Context: ${context.substring(0, 100)}. `
    }
    
    prompt += `Clean, minimalist design with vibrant colors, suitable for a blog article. High quality, detailed, professional photography style.`
    
    return prompt
  }

  /**
   * Download and convert image to base64 (for storage)
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const base64 = Buffer.from(arrayBuffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'
      
      return `data:${mimeType};base64,${base64}`
    } catch (error) {
      console.error('❌ Failed to download image:', error)
      throw error
    }
  }
}

// Export singleton instance
export const ideogramService = new IdeogramService()
