/**
 * Ideogram API Service
 * Handles image generation using Ideogram's V_2A_TURBO model
 */

export interface IdeogramImageRequest {
  prompt: string
  aspect_ratio?: 'ASPECT_10_16' | 'ASPECT_16_10' | 'ASPECT_1_1' | 'ASPECT_9_16' | 'ASPECT_16_9'
  model?: 'V_2A_TURBO' | 'V_2A' | 'V_2' | 'V_1_TURBO' | 'V_1'
  magic_prompt_option?: 'AUTO' | 'ON' | 'OFF'
  seed?: number
  style_type?: 'AUTO' | 'GENERAL' | 'REALISTIC' | 'DESIGN' | 'RENDER_3D' | 'ANIME'
}

export interface IdeogramImageResponse {
  created: string
  data: Array<{
    prompt: string
    resolution: string
    is_image_safe: boolean
    seed: number
    url: string
    style_type: string
  }>
}

export class IdeogramService {
  private apiKey: string
  private baseUrl = 'https://api.ideogram.ai'

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.IDEOGRAM_API_KEY || ''
    // Only warn if no API key is provided at all
    if (!this.apiKey && !apiKey) {
      console.warn('⚠️ Ideogram API key not configured')
    }
  }

  /**
   * Generate an image using Ideogram API
   */
  async generateImage(request: IdeogramImageRequest): Promise<IdeogramImageResponse> {
    if (!this.apiKey) {
      throw new Error('Ideogram API key not configured')
    }

    try {
      console.log(`🎨 Generating image with Ideogram: "${request.prompt.substring(0, 50)}..."`)

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          image_request: {
            prompt: request.prompt,
            aspect_ratio: request.aspect_ratio || 'ASPECT_16_9',
            model: request.model || 'V_2A_TURBO',
            magic_prompt_option: request.magic_prompt_option || 'AUTO',
            style_type: request.style_type || 'AUTO',
            ...(request.seed && { seed: request.seed })
          }
        })
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Ideogram API error ${response.status}: ${errorText}`)
      }

      const result: IdeogramImageResponse = await response.json()
      
      if (!result.data || result.data.length === 0) {
        throw new Error('No images generated')
      }

      console.log(`✅ Image generated successfully: ${result.data[0].url}`)
      return result

    } catch (error) {
      console.error('❌ Ideogram image generation failed:', error)
      throw error
    }
  }

  /**
   * Generate an image optimized for blog headings using Gemini-enhanced prompts
   */
  async generateBlogHeadingImage(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    const enhancedPrompt = await this.createEnhancedBlogImagePrompt(headingText, context, blogTitle)

    const result = await this.generateImage({
      prompt: enhancedPrompt,
      aspect_ratio: 'ASPECT_16_9',
      model: 'V_2A_TURBO',
      magic_prompt_option: 'ON', // Enable magic prompt for better results
      style_type: 'DESIGN' // Better for typography and professional designs
    })

    return result.data[0].url
  }

  /**
   * Create an enhanced prompt using Gemini for better image generation
   */
  private async createEnhancedBlogImagePrompt(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    try {
      // Use Gemini to create a better prompt
      const geminiPrompt = await this.generatePromptWithGemini(headingText, context, blogTitle)

      // Combine with typography-focused instructions
      const enhancedPrompt = `${geminiPrompt}

VISUAL & TYPOGRAPHY INTEGRATION REQUIREMENTS:
- MUST include the exact text "${headingText}" prominently but naturally integrated
- Use elegant, modern typography that complements the visual elements
- Text should be readable and well-positioned within the overall composition
- High contrast between text and background for readability
- Balance typography with relevant visual elements that represent the topic
- Include illustrative elements, icons, objects, or imagery related to the heading content
- Professional color palette that matches the topic's industry and mood
- Visual elements should enhance and support the text message, not compete with it
- Create a cohesive design where text and visuals work together
- 16:9 aspect ratio, horizontal layout optimized for blog headers
- Modern, engaging design that tells a visual story about the topic
- Avoid purely text-only designs - include relevant visual context
- Design should be informative and visually appealing to draw readers in
- Visual elements should make the topic more understandable and engaging`

      return enhancedPrompt
    } catch (error) {
      console.warn('Failed to enhance prompt with Gemini, using fallback:', error)
      return this.createFallbackBlogImagePrompt(headingText, context)
    }
  }

  /**
   * Generate enhanced prompt using Gemini
   */
  private async generatePromptWithGemini(headingText: string, context?: string, blogTitle?: string): Promise<string> {
    const geminiPromptRequest = `Create a detailed, creative prompt for generating a professional blog header image that balances typography with relevant visual elements.

HEADING TEXT TO DISPLAY: "${headingText}"
${blogTitle ? `BLOG TITLE: "${blogTitle}"` : ''}
${context ? `CONTENT CONTEXT: ${context.substring(0, 300)}` : ''}

Create a prompt for a visually engaging header image that will:

1. **Typography Integration**: Include the heading text "${headingText}" prominently but naturally integrated with the visual design
2. **Visual Storytelling**: Create illustrative elements, icons, symbols, or imagery that directly relate to the topic and enhance understanding
3. **Content Representation**: Include visual metaphors, objects, or scenes that represent what the heading is discussing
4. **Design Variety**: Choose from different styles based on content:
   - Illustrative/iconic for how-to or educational content
   - Photographic/realistic for product or service topics
   - Abstract/conceptual for strategy or theoretical topics
   - Infographic-style for data or process topics
5. **Color Psychology**: Use colors that match the topic's mood and industry (tech blues, nature greens, business navy, creative purples, etc.)
6. **Visual Hierarchy**: Balance text prominence with supporting visual elements that tell the story

Requirements:
- Text should be readable and well-integrated, not just overlaid
- Include relevant visual elements that represent the topic
- Professional quality suitable for blog headers
- 16:9 aspect ratio, horizontal layout
- Modern, engaging design that draws readers in
- Visual elements should support and enhance the text message

Return only the detailed image generation prompt, no explanations or additional text.`

    try {
      // Check if we're in a server environment
      if (typeof window !== 'undefined') {
        // Client-side: make API call
        const response = await fetch('/api/gemini-prompt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: geminiPromptRequest
          })
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))
          throw new Error(errorData.error || `HTTP ${response.status}`)
        }

        const data = await response.json()
        return data.prompt || this.createFallbackBlogImagePrompt(headingText, context)
      } else {
        // Server-side: use Gemini service directly
        const { GeminiService } = await import('./gemini')
        const gemini = new GeminiService()

        const result = await gemini.generateContent(geminiPromptRequest, {
          temperature: 0.8,
          maxOutputTokens: 1000,
          thinkingConfig: {
            thinkingBudget: 0,
            includeThoughts: false
          }
        })

        return result.response.trim() || this.createFallbackBlogImagePrompt(headingText, context)
      }
    } catch (error) {
      console.warn('Gemini prompt generation failed, using fallback:', error instanceof Error ? error.message : error)
      return this.createFallbackBlogImagePrompt(headingText, context)
    }
  }

  /**
   * Fallback prompt creation method with balanced visual and typography elements
   */
  private createFallbackBlogImagePrompt(headingText: string, context?: string): string {
    const cleanHeading = headingText.replace(/[#*_`]/g, '').trim()

    let prompt = `Professional blog header image featuring the text "${cleanHeading}" integrated with relevant visual elements. `

    // Add context-based visual elements and design styles
    if (context) {
      const contextLower = context.toLowerCase()
      if (contextLower.includes('technology') || contextLower.includes('tech') || contextLower.includes('digital') || contextLower.includes('ai') || contextLower.includes('software')) {
        prompt += `Include tech-related visual elements like circuit patterns, digital interfaces, computer screens, or futuristic geometric shapes. Use blue and cyan color palette with modern typography. `
      } else if (contextLower.includes('business') || contextLower.includes('strategy') || contextLower.includes('growth') || contextLower.includes('marketing')) {
        prompt += `Include business-related visuals like growth charts, arrows pointing upward, professional icons, or corporate elements. Use navy, gold, and white color scheme with clean typography. `
      } else if (contextLower.includes('design') || contextLower.includes('creative') || contextLower.includes('art') || contextLower.includes('visual')) {
        prompt += `Include creative design elements like color palettes, design tools, artistic brushstrokes, or creative workspace items. Use vibrant, creative color scheme with stylish typography. `
      } else if (contextLower.includes('guide') || contextLower.includes('tutorial') || contextLower.includes('how to') || contextLower.includes('steps')) {
        prompt += `Include instructional visual elements like step indicators, arrows, checklists, or educational icons. Use clear, friendly colors with readable typography. `
      } else if (contextLower.includes('data') || contextLower.includes('analytics') || contextLower.includes('report') || contextLower.includes('metrics')) {
        prompt += `Include data visualization elements like charts, graphs, dashboard interfaces, or statistical icons. Use professional blue and gray tones with clean typography. `
      } else {
        prompt += `Include relevant visual metaphors and professional elements that relate to the topic. Use appropriate color scheme that matches the content mood. `
      }
    } else {
      prompt += `Include engaging visual elements that complement the text with professional color scheme and modern design. `
    }

    prompt += `Text should be well-integrated with visuals, high contrast for readability, 16:9 aspect ratio, modern professional design suitable for blog header that engages readers visually.`

    return prompt
  }

  /**
   * Download and convert image to base64 (for storage)
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    try {
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const base64 = Buffer.from(arrayBuffer).toString('base64')
      const mimeType = response.headers.get('content-type') || 'image/jpeg'
      
      return `data:${mimeType};base64,${base64}`
    } catch (error) {
      console.error('❌ Failed to download image:', error)
      throw error
    }
  }
}

// Export singleton instance
export const ideogramService = new IdeogramService()
