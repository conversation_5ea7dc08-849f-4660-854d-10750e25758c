import { prisma } from './prisma'
import { createGmailService } from './gmail'

export interface ScheduledEmailJob {
  id: string
  userId: string
  to: string[]
  cc?: string[]
  bcc?: string[]
  subject: string
  body: string
  isHtml: boolean
  scheduleTime: Date
  status: 'pending' | 'sent' | 'failed' | 'cancelled'
  attempts: number
  lastAttempt?: Date
  error?: string
}

export class EmailScheduler {
  private static instance: EmailScheduler
  private isRunning = false
  private checkInterval: NodeJS.Timeout | null = null

  private constructor() {}

  static getInstance(): EmailScheduler {
    if (!EmailScheduler.instance) {
      EmailScheduler.instance = new EmailScheduler()
    }
    return EmailScheduler.instance
  }

  /**
   * Start the email scheduler
   */
  start(intervalMs: number = 60000) { // Check every minute by default
    if (this.isRunning) {
      console.log('⏰ Email scheduler is already running')
      return
    }

    console.log('⏰ Starting email scheduler...')
    this.isRunning = true
    
    this.checkInterval = setInterval(() => {
      this.processScheduledEmails()
    }, intervalMs)

    // Process immediately on start
    this.processScheduledEmails()
  }

  /**
   * Stop the email scheduler
   */
  stop() {
    if (!this.isRunning) {
      console.log('⏰ Email scheduler is not running')
      return
    }

    console.log('⏰ Stopping email scheduler...')
    this.isRunning = false
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  /**
   * Process all scheduled emails that are due
   */
  private async processScheduledEmails() {
    try {
      console.log('⏰ Checking for scheduled emails...')
      
      // Get all pending scheduled emails that are due
      const dueEmails = await this.getDueEmails()
      
      if (dueEmails.length === 0) {
        console.log('⏰ No scheduled emails due')
        return
      }

      console.log(`⏰ Found ${dueEmails.length} emails to send`)

      // Process each email
      for (const email of dueEmails) {
        await this.processEmail(email)
      }

    } catch (error) {
      console.error('⏰ Error processing scheduled emails:', error)
    }
  }

  /**
   * Get emails that are due to be sent
   */
  private async getDueEmails(): Promise<ScheduledEmailJob[]> {
    const now = new Date()
    
    const scheduledContent = await prisma.content.findMany({
      where: {
        type: 'email_scheduled',
        createdAt: {
          lte: now
        }
      },
      orderBy: {
        createdAt: 'asc'
      },
      take: 50 // Process max 50 emails at a time
    })

    const emails: ScheduledEmailJob[] = []
    
    for (const content of scheduledContent) {
      try {
        const metadata = JSON.parse(content.metadata || '{}')
        
        // Skip if not pending or schedule time not reached
        if (metadata.status !== 'pending') continue
        
        const scheduleTime = new Date(metadata.scheduleTime)
        if (scheduleTime > now) continue

        emails.push({
          id: content.id,
          userId: content.userId,
          to: metadata.to || [],
          cc: metadata.cc,
          bcc: metadata.bcc,
          subject: content.title.replace('Scheduled: ', ''),
          body: content.content,
          isHtml: metadata.isHtml || false,
          scheduleTime: scheduleTime,
          status: metadata.status || 'pending',
          attempts: metadata.attempts || 0,
          lastAttempt: metadata.lastAttempt ? new Date(metadata.lastAttempt) : undefined,
          error: metadata.error
        })
      } catch (parseError) {
        console.error('⏰ Error parsing scheduled email metadata:', parseError)
      }
    }

    return emails
  }

  /**
   * Process a single scheduled email
   */
  private async processEmail(email: ScheduledEmailJob) {
    try {
      console.log(`⏰ Processing scheduled email: ${email.id}`)

      // Get user's access tokens (this would need to be stored securely)
      // For now, we'll try to get them from the user's session
      const user = await prisma.user.findUnique({
        where: { id: email.userId },
        include: { accounts: true }
      })

      if (!user || !user.accounts.length) {
        throw new Error('User or Gmail account not found')
      }

      // Find Google account
      const googleAccount = user.accounts.find(account => account.provider === 'google')
      if (!googleAccount || !googleAccount.access_token) {
        throw new Error('Gmail access token not found')
      }

      // Create Gmail service
      const gmailService = createGmailService(
        googleAccount.access_token,
        googleAccount.refresh_token || undefined
      )

      // Send the email
      const sendResult = await gmailService.sendEmail({
        to: email.to,
        cc: email.cc,
        bcc: email.bcc,
        subject: email.subject,
        body: email.body,
        isHtml: email.isHtml
      })

      if (sendResult.success) {
        console.log(`✅ Scheduled email sent successfully: ${email.id}`)
        await this.updateEmailStatus(email.id, 'sent', { messageId: sendResult.messageId })
      } else {
        console.error(`❌ Failed to send scheduled email: ${email.id} - ${sendResult.error}`)
        await this.updateEmailStatus(email.id, 'failed', { error: sendResult.error }, email.attempts + 1)
      }

    } catch (error) {
      console.error(`❌ Error processing scheduled email ${email.id}:`, error)
      await this.updateEmailStatus(
        email.id, 
        'failed', 
        { error: error instanceof Error ? error.message : 'Unknown error' },
        email.attempts + 1
      )
    }
  }

  /**
   * Update the status of a scheduled email
   */
  private async updateEmailStatus(
    emailId: string, 
    status: 'sent' | 'failed' | 'cancelled',
    additionalData: { [key: string]: any } = {},
    attempts?: number
  ) {
    try {
      const content = await prisma.content.findUnique({
        where: { id: emailId }
      })

      if (!content) {
        console.error(`⏰ Scheduled email not found: ${emailId}`)
        return
      }

      const metadata = JSON.parse(content.metadata || '{}')
      const updatedMetadata = {
        ...metadata,
        status,
        lastAttempt: new Date().toISOString(),
        attempts: attempts || metadata.attempts || 0,
        ...additionalData
      }

      await prisma.content.update({
        where: { id: emailId },
        data: {
          metadata: JSON.stringify(updatedMetadata)
        }
      })

      console.log(`⏰ Updated email ${emailId} status to: ${status}`)

    } catch (error) {
      console.error(`⏰ Error updating email status for ${emailId}:`, error)
    }
  }

  /**
   * Cancel a scheduled email
   */
  async cancelScheduledEmail(emailId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const content = await prisma.content.findFirst({
        where: {
          id: emailId,
          userId: userId,
          type: 'email_scheduled'
        }
      })

      if (!content) {
        return { success: false, error: 'Scheduled email not found' }
      }

      const metadata = JSON.parse(content.metadata || '{}')
      
      if (metadata.status !== 'pending') {
        return { success: false, error: `Cannot cancel email with status: ${metadata.status}` }
      }

      await this.updateEmailStatus(emailId, 'cancelled')
      
      return { success: true }

    } catch (error) {
      console.error('⏰ Error cancelling scheduled email:', error)
      return { success: false, error: 'Failed to cancel scheduled email' }
    }
  }

  /**
   * Get scheduled emails for a user
   */
  async getScheduledEmails(userId: string, status?: string): Promise<ScheduledEmailJob[]> {
    try {
      const whereClause: any = {
        userId: userId,
        type: 'email_scheduled'
      }

      const scheduledContent = await prisma.content.findMany({
        where: whereClause,
        orderBy: {
          createdAt: 'desc'
        },
        take: 100
      })

      const emails: ScheduledEmailJob[] = []
      
      for (const content of scheduledContent) {
        try {
          const metadata = JSON.parse(content.metadata || '{}')
          
          // Filter by status if provided
          if (status && metadata.status !== status) continue

          emails.push({
            id: content.id,
            userId: content.userId,
            to: metadata.to || [],
            cc: metadata.cc,
            bcc: metadata.bcc,
            subject: content.title.replace('Scheduled: ', ''),
            body: content.content,
            isHtml: metadata.isHtml || false,
            scheduleTime: new Date(metadata.scheduleTime),
            status: metadata.status || 'pending',
            attempts: metadata.attempts || 0,
            lastAttempt: metadata.lastAttempt ? new Date(metadata.lastAttempt) : undefined,
            error: metadata.error
          })
        } catch (parseError) {
          console.error('⏰ Error parsing scheduled email metadata:', parseError)
        }
      }

      return emails

    } catch (error) {
      console.error('⏰ Error getting scheduled emails:', error)
      return []
    }
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      hasInterval: this.checkInterval !== null
    }
  }
}

// Export singleton instance
export const emailScheduler = EmailScheduler.getInstance()

// Auto-start scheduler in production
if (process.env.NODE_ENV === 'production') {
  emailScheduler.start()
}