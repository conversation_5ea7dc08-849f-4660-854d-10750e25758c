import { google } from 'googleapis'
import { OAuth2Client } from 'google-auth-library'

export interface EmailMessage {
  to: string[]
  cc?: string[]
  bcc?: string[]
  subject: string
  body: string
  isHtml?: boolean
  attachments?: EmailAttachment[]
}

export interface EmailAttachment {
  filename: string
  content: string | Buffer
  contentType: string
}

export interface ScheduledEmail extends EmailMessage {
  scheduleTime: Date
  id?: string
}

export interface ReceivedEmail {
  id: string
  threadId: string
  from: string
  to: string[]
  subject: string
  body: string
  date: Date
  snippet: string
  isRead: boolean
}

export class GmailService {
  private oauth2Client: OAuth2Client
  private gmail: any

  constructor(accessToken: string, refreshToken?: string) {
    this.oauth2Client = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXTAUTH_URL + '/api/auth/callback/google'
    )

    this.oauth2Client.setCredentials({
      access_token: accessToken,
      refresh_token: refreshToken,
    })

    this.gmail = google.gmail({ version: 'v1', auth: this.oauth2Client })
  }

  /**
   * Send an email through Gmail API
   */
  async sendEmail(emailData: EmailMessage): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const emailLines = [
        `To: ${emailData.to.join(', ')}`,
        emailData.cc && emailData.cc.length > 0 ? `Cc: ${emailData.cc.join(', ')}` : '',
        emailData.bcc && emailData.bcc.length > 0 ? `Bcc: ${emailData.bcc.join(', ')}` : '',
        `Subject: ${emailData.subject}`,
        emailData.isHtml ? 'Content-Type: text/html; charset=utf-8' : 'Content-Type: text/plain; charset=utf-8',
        '',
        emailData.body
      ].filter(line => line !== '')

      const email = emailLines.join('\r\n')
      const encodedEmail = Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')

      const response = await this.gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedEmail,
        },
      })

      console.log('✅ Email sent successfully:', response.data.id)
      return { success: true, messageId: response.data.id }
    } catch (error) {
      console.error('❌ Failed to send email:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Create a draft email
   */
  async createDraft(emailData: EmailMessage): Promise<{ success: boolean; draftId?: string; error?: string }> {
    try {
      const emailLines = [
        `To: ${emailData.to.join(', ')}`,
        emailData.cc && emailData.cc.length > 0 ? `Cc: ${emailData.cc.join(', ')}` : '',
        emailData.bcc && emailData.bcc.length > 0 ? `Bcc: ${emailData.bcc.join(', ')}` : '',
        `Subject: ${emailData.subject}`,
        emailData.isHtml ? 'Content-Type: text/html; charset=utf-8' : 'Content-Type: text/plain; charset=utf-8',
        '',
        emailData.body
      ].filter(line => line !== '')

      const email = emailLines.join('\r\n')
      const encodedEmail = Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '')

      const response = await this.gmail.users.drafts.create({
        userId: 'me',
        requestBody: {
          message: {
            raw: encodedEmail,
          },
        },
      })

      console.log('✅ Draft created successfully:', response.data.id)
      return { success: true, draftId: response.data.id }
    } catch (error) {
      console.error('❌ Failed to create draft:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get recent emails from Gmail
   */
  async getRecentEmails(maxResults: number = 10): Promise<{ success: boolean; emails?: ReceivedEmail[]; error?: string }> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        maxResults: maxResults,
        q: 'in:inbox',
      })

      if (!response.data.messages) {
        return { success: true, emails: [] }
      }

      const emails: ReceivedEmail[] = []
      
      for (const message of response.data.messages) {
        const emailDetail = await this.gmail.users.messages.get({
          userId: 'me',
          id: message.id,
          format: 'full',
        })

        const headers = emailDetail.data.payload.headers
        const fromHeader = headers.find((h: any) => h.name === 'From')
        const toHeader = headers.find((h: any) => h.name === 'To')
        const subjectHeader = headers.find((h: any) => h.name === 'Subject')
        const dateHeader = headers.find((h: any) => h.name === 'Date')

        // Extract body
        let body = ''
        if (emailDetail.data.payload.body?.data) {
          body = Buffer.from(emailDetail.data.payload.body.data, 'base64').toString()
        } else if (emailDetail.data.payload.parts) {
          const textPart = emailDetail.data.payload.parts.find((part: any) => part.mimeType === 'text/plain')
          if (textPart?.body?.data) {
            body = Buffer.from(textPart.body.data, 'base64').toString()
          }
        }

        emails.push({
          id: emailDetail.data.id,
          threadId: emailDetail.data.threadId,
          from: fromHeader?.value || '',
          to: toHeader?.value ? toHeader.value.split(',').map((email: string) => email.trim()) : [],
          subject: subjectHeader?.value || '',
          body: body,
          date: new Date(dateHeader?.value || emailDetail.data.internalDate),
          snippet: emailDetail.data.snippet || '',
          isRead: !emailDetail.data.labelIds?.includes('UNREAD')
        })
      }

      console.log(`✅ Retrieved ${emails.length} emails successfully`)
      return { success: true, emails }
    } catch (error) {
      console.error('❌ Failed to get emails:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get Gmail profile information
   */
  async getProfile(): Promise<{ success: boolean; profile?: any; error?: string }> {
    try {
      const response = await this.gmail.users.getProfile({
        userId: 'me',
      })

      console.log('✅ Gmail profile retrieved successfully')
      return { success: true, profile: response.data }
    } catch (error) {
      console.error('❌ Failed to get Gmail profile:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Search emails with a query
   */
  async searchEmails(query: string, maxResults: number = 10): Promise<{ success: boolean; emails?: ReceivedEmail[]; error?: string }> {
    try {
      const response = await this.gmail.users.messages.list({
        userId: 'me',
        maxResults: maxResults,
        q: query,
      })

      if (!response.data.messages) {
        return { success: true, emails: [] }
      }

      const emails: ReceivedEmail[] = []
      
      for (const message of response.data.messages) {
        const emailDetail = await this.gmail.users.messages.get({
          userId: 'me',
          id: message.id,
          format: 'full',
        })

        const headers = emailDetail.data.payload.headers
        const fromHeader = headers.find((h: any) => h.name === 'From')
        const toHeader = headers.find((h: any) => h.name === 'To')
        const subjectHeader = headers.find((h: any) => h.name === 'Subject')
        const dateHeader = headers.find((h: any) => h.name === 'Date')

        let body = ''
        if (emailDetail.data.payload.body?.data) {
          body = Buffer.from(emailDetail.data.payload.body.data, 'base64').toString()
        } else if (emailDetail.data.payload.parts) {
          const textPart = emailDetail.data.payload.parts.find((part: any) => part.mimeType === 'text/plain')
          if (textPart?.body?.data) {
            body = Buffer.from(textPart.body.data, 'base64').toString()
          }
        }

        emails.push({
          id: emailDetail.data.id,
          threadId: emailDetail.data.threadId,
          from: fromHeader?.value || '',
          to: toHeader?.value ? toHeader.value.split(',').map((email: string) => email.trim()) : [],
          subject: subjectHeader?.value || '',
          body: body,
          date: new Date(dateHeader?.value || emailDetail.data.internalDate),
          snippet: emailDetail.data.snippet || '',
          isRead: !emailDetail.data.labelIds?.includes('UNREAD')
        })
      }

      console.log(`✅ Found ${emails.length} emails for query: ${query}`)
      return { success: true, emails }
    } catch (error) {
      console.error('❌ Failed to search emails:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Mark email as read
   */
  async markAsRead(messageId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await this.gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          removeLabelIds: ['UNREAD']
        }
      })

      console.log('✅ Email marked as read:', messageId)
      return { success: true }
    } catch (error) {
      console.error('❌ Failed to mark email as read:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Check if the access token is valid
   */
  async checkConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      await this.gmail.users.getProfile({
        userId: 'me',
      })
      return { success: true }
    } catch (error) {
      console.error('❌ Gmail connection failed:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }
}

/**
 * Create a Gmail service instance from session
 */
export function createGmailService(accessToken: string, refreshToken?: string): GmailService {
  return new GmailService(accessToken, refreshToken)
}

/**
 * Validate email address format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Parse email addresses from a string
 */
export function parseEmailAddresses(emailString: string): string[] {
  return emailString
    .split(/[,;]/)
    .map(email => email.trim())
    .filter(email => email && isValidEmail(email))
}