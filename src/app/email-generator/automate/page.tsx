'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  Mail, 
  ArrowLeft, 
  Settings, 
  Send, 
  Calendar, 
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Plus,
  Trash2,
  RefreshCw,
  User,
  Search,
  Filter
} from 'lucide-react'
import Link from 'next/link'

interface EmailMessage {
  id: string
  threadId: string
  from: string
  to: string[]
  subject: string
  body: string
  date: Date
  snippet: string
  isRead: boolean
}

interface ScheduledEmail {
  id: string
  subject: string
  body: string
  to: string[]
  cc: string[]
  bcc: string[]
  scheduleTime: string
  status: string
  createdAt: string
  isHtml: boolean
}

interface GmailProfile {
  emailAddress: string
  messagesTotal: number
  threadsTotal: number
}

export default function GmailAutomatePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState('overview')
  const [gmailConnected, setGmailConnected] = useState(false)
  const [gmailProfile, setGmailProfile] = useState<GmailProfile | null>(null)
  const [recentEmails, setRecentEmails] = useState<EmailMessage[]>([])
  const [scheduledEmails, setScheduledEmails] = useState<ScheduledEmail[]>([])
  const [loading, setLoading] = useState(true)
  const [emailLoading, setEmailLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Email composer state
  const [composerOpen, setComposerOpen] = useState(false)
  const [emailForm, setEmailForm] = useState({
    to: '',
    cc: '',
    bcc: '',
    subject: '',
    body: '',
    isHtml: false,
    scheduleTime: ''
  })
  const [sending, setSending] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Check Gmail connection on mount
  useEffect(() => {
    if (session) {
      checkGmailConnection()
    }
  }, [session])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-blue-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const checkGmailConnection = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/gmail/auth')
      const data = await response.json()
      
      if (data.success && data.connected) {
        setGmailConnected(true)
        setGmailProfile(data.profile)
        await Promise.all([
          loadRecentEmails(),
          loadScheduledEmails()
        ])
      } else {
        setGmailConnected(false)
      }
    } catch (error) {
      console.error('Failed to check Gmail connection:', error)
      setGmailConnected(false)
    } finally {
      setLoading(false)
    }
  }

  const loadRecentEmails = async () => {
    try {
      setEmailLoading(true)
      const response = await fetch(`/api/gmail/messages?maxResults=20&query=${encodeURIComponent(searchQuery)}`)
      const data = await response.json()
      
      if (data.success) {
        setRecentEmails(data.emails.map((email: any) => ({
          ...email,
          date: new Date(email.date)
        })))
      }
    } catch (error) {
      console.error('Failed to load emails:', error)
    } finally {
      setEmailLoading(false)
    }
  }

  const loadScheduledEmails = async () => {
    try {
      const response = await fetch('/api/gmail/schedule')
      const data = await response.json()
      
      if (data.success) {
        setScheduledEmails(data.emails)
      }
    } catch (error) {
      console.error('Failed to load scheduled emails:', error)
    }
  }

  const handleSendEmail = async (isScheduled: boolean = false) => {
    try {
      setSending(true)
      
      const endpoint = isScheduled ? '/api/gmail/schedule' : '/api/gmail/send'
      const payload = {
        to: emailForm.to,
        cc: emailForm.cc || undefined,
        bcc: emailForm.bcc || undefined,
        subject: emailForm.subject,
        body: emailForm.body,
        isHtml: emailForm.isHtml,
        ...(isScheduled && { scheduleTime: emailForm.scheduleTime })
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      })

      const data = await response.json()
      
      if (data.success) {
        setComposerOpen(false)
        setEmailForm({
          to: '',
          cc: '',
          bcc: '',
          subject: '',
          body: '',
          isHtml: false,
          scheduleTime: ''
        })
        
        if (isScheduled) {
          await loadScheduledEmails()
        }
        
        alert(`Email ${isScheduled ? 'scheduled' : 'sent'} successfully!`)
      } else {
        alert(`Error: ${data.error}`)
      }
    } catch (error) {
      alert('Failed to send email')
    } finally {
      setSending(false)
    }
  }

  const handleCancelScheduled = async (scheduleId: string) => {
    try {
      const response = await fetch(`/api/gmail/schedule?id=${scheduleId}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      
      if (data.success) {
        await loadScheduledEmails()
        alert('Scheduled email cancelled successfully')
      } else {
        alert(`Error: ${data.error}`)
      }
    } catch (error) {
      alert('Failed to cancel scheduled email')
    }
  }

  const reauthenticateGmail = async () => {
    try {
      const response = await fetch('/api/gmail/auth', { method: 'POST' })
      const data = await response.json()
      
      if (data.success && data.authUrl) {
        window.location.href = data.authUrl
      }
    } catch (error) {
      console.error('Failed to initiate re-authentication:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-blue-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Checking Gmail connection...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900/20 to-slate-900" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-blue-500/10 rounded-full blur-[100px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/email-generator">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600">
                    <Settings className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Email Automation</h1>
                    <p className="text-gray-400">Gmail Integration & Management</p>
                  </div>
                </div>
              </div>
              
              {gmailConnected && (
                <motion.button
                  onClick={() => setComposerOpen(true)}
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all"
                >
                  <Plus className="w-5 h-5" />
                  <span>Compose Email</span>
                </motion.button>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {!gmailConnected ? (
            // Gmail Connection Setup
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center max-w-2xl mx-auto"
            >
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-12">
                <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 w-fit mx-auto mb-6">
                  <Mail className="w-12 h-12 text-blue-400" />
                </div>
                <h2 className="text-3xl font-bold text-white mb-4">Connect Your Gmail Account</h2>
                <p className="text-gray-300 mb-8">
                  To use email automation features, you need to connect your Gmail account. 
                  This will allow you to send, schedule, and manage emails directly from our platform.
                </p>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-center space-x-3 text-left">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">Send emails directly through Gmail</span>
                  </div>
                  <div className="flex items-center space-x-3 text-left">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">Schedule emails for later delivery</span>
                  </div>
                  <div className="flex items-center space-x-3 text-left">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">Access and manage your inbox</span>
                  </div>
                  <div className="flex items-center space-x-3 text-left">
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                    <span className="text-gray-300">Secure OAuth2 authentication</span>
                  </div>
                </div>

                <motion.button
                  onClick={reauthenticateGmail}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all font-semibold flex items-center space-x-2 mx-auto"
                >
                  <Mail className="w-5 h-5" />
                  <span>Connect Gmail Account</span>
                </motion.button>
                
                <p className="text-sm text-gray-400 mt-4">
                  Your data is secure and we only request necessary permissions
                </p>
              </div>
            </motion.div>
          ) : (
            // Gmail Connected - Main Interface
            <div className="space-y-8">
              {/* Gmail Profile & Stats */}
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-2xl bg-gradient-to-br from-green-600 to-emerald-600">
                      <CheckCircle className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white">Gmail Connected</h3>
                      <p className="text-gray-400">{gmailProfile?.emailAddress}</p>
                    </div>
                  </div>
                  <motion.button
                    onClick={checkGmailConnection}
                    whileHover={{ scale: 1.05 }}
                    className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                  >
                    <RefreshCw className="w-4 h-4 text-white" />
                  </motion.button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{gmailProfile?.messagesTotal || 0}</div>
                    <div className="text-gray-400">Total Messages</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{scheduledEmails.length}</div>
                    <div className="text-gray-400">Scheduled Emails</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{recentEmails.filter(e => !e.isRead).length}</div>
                    <div className="text-gray-400">Unread Messages</div>
                  </div>
                </div>
              </div>

              {/* Tabs */}
              <div className="flex space-x-1 bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-1">
                {[
                  { id: 'overview', label: 'Overview', icon: Eye },
                  { id: 'inbox', label: 'Inbox', icon: Mail },
                  { id: 'scheduled', label: 'Scheduled', icon: Calendar }
                ].map((tab) => (
                  <motion.button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl font-medium transition-all ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                        : 'text-gray-400 hover:text-white hover:bg-white/10'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                  </motion.button>
                ))}
              </div>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Recent Activity */}
                  <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">Recent Activity</h3>
                    <div className="space-y-4">
                      {recentEmails.slice(0, 5).map((email) => (
                        <div key={email.id} className="flex items-start space-x-3 p-3 bg-white/5 rounded-xl">
                          <div className={`w-2 h-2 rounded-full mt-2 ${email.isRead ? 'bg-gray-400' : 'bg-blue-400'}`}></div>
                          <div className="flex-1 min-w-0">
                            <div className="text-white font-medium truncate">{email.subject}</div>
                            <div className="text-gray-400 text-sm truncate">{email.from}</div>
                            <div className="text-gray-500 text-xs">{email.date.toLocaleDateString()}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Upcoming Scheduled */}
                  <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">Upcoming Scheduled</h3>
                    <div className="space-y-4">
                      {scheduledEmails.filter(e => e.status === 'pending').slice(0, 5).map((email) => (
                        <div key={email.id} className="flex items-start space-x-3 p-3 bg-white/5 rounded-xl">
                          <Clock className="w-5 h-5 text-blue-400 mt-1 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="text-white font-medium truncate">{email.subject}</div>
                            <div className="text-gray-400 text-sm truncate">To: {email.to.join(', ')}</div>
                            <div className="text-gray-500 text-xs">
                              {new Date(email.scheduleTime).toLocaleString()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'inbox' && (
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-white">Inbox</h3>
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                        <input
                          type="text"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && loadRecentEmails()}
                          placeholder="Search emails..."
                          className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50"
                        />
                      </div>
                      <motion.button
                        onClick={loadRecentEmails}
                        whileHover={{ scale: 1.05 }}
                        className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                      >
                        <RefreshCw className="w-4 h-4 text-white" />
                      </motion.button>
                    </div>
                  </div>

                  {emailLoading ? (
                    <div className="text-center py-8">
                      <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                      <p className="text-gray-400">Loading emails...</p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {recentEmails.map((email) => (
                        <div key={email.id} className={`p-4 rounded-xl border transition-all hover:bg-white/10 ${
                          email.isRead ? 'bg-white/5 border-white/10' : 'bg-blue-500/10 border-blue-500/20'
                        }`}>
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-2">
                                <div className={`w-2 h-2 rounded-full ${email.isRead ? 'bg-gray-400' : 'bg-blue-400'}`}></div>
                                <span className="text-white font-medium truncate">{email.subject}</span>
                              </div>
                              <div className="text-gray-400 text-sm mb-1">From: {email.from}</div>
                              <div className="text-gray-300 text-sm line-clamp-2">{email.snippet}</div>
                            </div>
                            <div className="text-gray-500 text-xs ml-4 flex-shrink-0">
                              {email.date.toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'scheduled' && (
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-white">Scheduled Emails</h3>
                    <motion.button
                      onClick={loadScheduledEmails}
                      whileHover={{ scale: 1.05 }}
                      className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                    >
                      <RefreshCw className="w-4 h-4 text-white" />
                    </motion.button>
                  </div>

                  <div className="space-y-4">
                    {scheduledEmails.map((email) => (
                      <div key={email.id} className="p-4 bg-white/5 rounded-xl border border-white/10">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-2">
                              <Clock className="w-4 h-4 text-blue-400" />
                              <span className="text-white font-medium truncate">{email.subject}</span>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                email.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                                email.status === 'sent' ? 'bg-green-500/20 text-green-400' :
                                'bg-red-500/20 text-red-400'
                              }`}>
                                {email.status}
                              </span>
                            </div>
                            <div className="text-gray-400 text-sm mb-1">To: {email.to.join(', ')}</div>
                            <div className="text-gray-400 text-sm">
                              Scheduled: {new Date(email.scheduleTime).toLocaleString()}
                            </div>
                          </div>
                          {email.status === 'pending' && (
                            <motion.button
                              onClick={() => handleCancelScheduled(email.id)}
                              whileHover={{ scale: 1.05 }}
                              className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all"
                            >
                              <Trash2 className="w-4 h-4" />
                            </motion.button>
                          )}
                        </div>
                      </div>
                    ))}

                    {scheduledEmails.length === 0 && (
                      <div className="text-center py-8">
                        <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-400">No scheduled emails</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Email Composer Modal */}
      {composerOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-white">Compose Email</h3>
              <button
                onClick={() => setComposerOpen(false)}
                className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Recipients */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">To *</label>
                <input
                  type="text"
                  value={emailForm.to}
                  onChange={(e) => setEmailForm({...emailForm, to: e.target.value})}
                  placeholder="<EMAIL>, <EMAIL>"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50"
                />
              </div>

              {/* CC & BCC */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">CC</label>
                  <input
                    type="text"
                    value={emailForm.cc}
                    onChange={(e) => setEmailForm({...emailForm, cc: e.target.value})}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">BCC</label>
                  <input
                    type="text"
                    value={emailForm.bcc}
                    onChange={(e) => setEmailForm({...emailForm, bcc: e.target.value})}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50"
                  />
                </div>
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Subject *</label>
                <input
                  type="text"
                  value={emailForm.subject}
                  onChange={(e) => setEmailForm({...emailForm, subject: e.target.value})}
                  placeholder="Email subject"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50"
                />
              </div>

              {/* Body */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Message *</label>
                <textarea
                  value={emailForm.body}
                  onChange={(e) => setEmailForm({...emailForm, body: e.target.value})}
                  placeholder="Your email message..."
                  rows={8}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50 resize-none"
                />
              </div>

              {/* Schedule Time (Optional) */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Schedule for Later (Optional)</label>
                <input
                  type="datetime-local"
                  value={emailForm.scheduleTime}
                  onChange={(e) => setEmailForm({...emailForm, scheduleTime: e.target.value})}
                  min={new Date().toISOString().slice(0, 16)}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-blue-500/50"
                />
              </div>

              {/* HTML Checkbox */}
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isHtml"
                  checked={emailForm.isHtml}
                  onChange={(e) => setEmailForm({...emailForm, isHtml: e.target.checked})}
                  className="w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500"
                />
                <label htmlFor="isHtml" className="text-sm text-gray-300">Send as HTML</label>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-4">
                <motion.button
                  onClick={() => handleSendEmail(false)}
                  disabled={sending || !emailForm.to || !emailForm.subject || !emailForm.body}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                >
                  {sending ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Sending...</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      <span>Send Now</span>
                    </>
                  )}
                </motion.button>

                {emailForm.scheduleTime && (
                  <motion.button
                    onClick={() => handleSendEmail(true)}
                    disabled={sending || !emailForm.to || !emailForm.subject || !emailForm.body}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex-1 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                  >
                    <Calendar className="w-5 h-5" />
                    <span>Schedule</span>
                  </motion.button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}