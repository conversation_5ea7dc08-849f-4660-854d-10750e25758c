'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  Mail, 
  Zap, 
  ArrowLeft, 
  Bot, 
  Settings, 
  Calendar, 
  Send, 
  PenTool,
  Sparkles,
  Gmail,
  Clock,
  MessageSquare
} from 'lucide-react'
import Link from 'next/link'

export default function EmailGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const tiles = [
    {
      id: 'automate',
      title: 'Automate',
      description: 'Connect your Gmail account to send, schedule, and manage emails automatically',
      icon: Settings,
      gradient: 'from-blue-600 to-purple-600',
      hoverGradient: 'from-blue-700 to-purple-700',
      features: [
        'Gmail Integration',
        'Schedule Emails',
        'Auto-Send',
        'Email Management'
      ],
      href: '/email-generator/automate'
    },
    {
      id: 'generate',
      title: 'Generate Email',
      description: 'Create professional emails using AI powered by Gemini 2.5 Flash Lite',
      icon: PenTool,
      gradient: 'from-emerald-600 to-teal-600',
      hoverGradient: 'from-emerald-700 to-teal-700',
      features: [
        'AI-Powered Writing',
        'Multiple Templates',
        'Tone Customization',
        'Bulk Generation'
      ],
      href: '/email-generator/generate'
    }
  ]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/20 to-slate-900" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-blue-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-emerald-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Email Generator</h1>
                    <p className="text-gray-400">AI-Powered Email Creation & Automation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-12">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-xl border border-white/10 rounded-full px-6 py-3 mb-6">
              <Sparkles className="w-5 h-5 text-emerald-400" />
              <span className="text-emerald-400 font-medium">Advanced Email Suite</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Professional Email Solutions
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Choose between AI-powered email generation or complete Gmail automation. 
              Create, schedule, and manage professional emails with cutting-edge technology.
            </p>
          </motion.div>

          {/* Tiles Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {tiles.map((tile, index) => (
              <motion.div
                key={tile.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2 }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Link href={tile.href}>
                  <div className="h-full bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 hover:bg-white/10 transition-all duration-300 cursor-pointer">
                    {/* Icon and Title */}
                    <div className="flex items-center space-x-4 mb-6">
                      <div className={`p-4 rounded-2xl bg-gradient-to-br ${tile.gradient} group-hover:bg-gradient-to-br group-hover:${tile.hoverGradient} transition-all`}>
                        <tile.icon className="w-8 h-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">{tile.title}</h3>
                        <p className="text-gray-400 mt-1">{tile.description}</p>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-3 mb-8">
                      {tile.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-3">
                          <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${tile.gradient}`}></div>
                          <span className="text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Action Button */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`w-full py-4 bg-gradient-to-r ${tile.gradient} text-white font-semibold rounded-xl hover:bg-gradient-to-r hover:${tile.hoverGradient} transition-all flex items-center justify-center space-x-2`}
                    >
                      {tile.id === 'automate' ? (
                        <>
                          <Settings className="w-5 h-5" />
                          <span>Setup Automation</span>
                        </>
                      ) : (
                        <>
                          <Zap className="w-5 h-5" />
                          <span>Start Generating</span>
                        </>
                      )}
                    </motion.div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Feature Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-20"
          >
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8">
              <h3 className="text-2xl font-bold text-white mb-8 text-center">
                Complete Email Workflow
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 w-fit mx-auto mb-4">
                    <Bot className="w-8 h-8 text-blue-400" />
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">AI Generation</h4>
                  <p className="text-gray-400">Powered by Gemini 2.5 Flash Lite for professional email creation</p>
                </div>
                <div className="text-center">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-emerald-600/20 to-teal-600/20 w-fit mx-auto mb-4">
                    <Gmail className="w-8 h-8 text-emerald-400" />
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">Gmail Integration</h4>
                  <p className="text-gray-400">Direct integration with your Gmail account for seamless sending</p>
                </div>
                <div className="text-center">
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-purple-600/20 to-pink-600/20 w-fit mx-auto mb-4">
                    <Clock className="w-8 h-8 text-purple-400" />
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">Smart Scheduling</h4>
                  <p className="text-gray-400">Schedule emails for optimal delivery times and automated follow-ups</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}