'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  PenTool, 
  ArrowLeft, 
  Zap, 
  Copy, 
  Download, 
  Send,
  Plus,
  X,
  Sparkles,
  FileText,
  Users,
  Calendar,
  Target,
  Mail,
  Settings,
  RefreshCw,
  BookOpen,
  Wand2
} from 'lucide-react'
import Link from 'next/link'

interface EmailTemplate {
  id: string
  name: string
  description: string
  category: string
  template: string
}

interface GenerationHistory {
  id: string
  type: string
  subject: string
  content: string
  createdAt: string
}

export default function EmailGeneratePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState('generate')
  const [emailType, setEmailType] = useState('marketing')
  const [formData, setFormData] = useState({
    purpose: '',
    audience: '',
    tone: 'professional',
    keyPoints: [''],
    context: '',
    callToAction: '',
    brandVoice: '',
    length: 'medium',
    urgency: 'normal',
    personalization: '',
    includeSubject: true
  })
  
  const [templates] = useState<EmailTemplate[]>([
    {
      id: 'welcome',
      name: 'Welcome Email',
      description: 'Onboard new users or customers',
      category: 'onboarding',
      template: 'Welcome to [Company]! We\'re excited to have you join our community...'
    },
    {
      id: 'product-launch',
      name: 'Product Launch',
      description: 'Announce new products or features',
      category: 'marketing',
      template: 'Introducing our latest innovation that will transform how you...'
    },
    {
      id: 'follow-up',
      name: 'Follow-up',
      description: 'Professional follow-up after meetings',
      category: 'business',
      template: 'Thank you for taking the time to meet with us today...'
    },
    {
      id: 'newsletter',
      name: 'Newsletter',
      description: 'Regular updates and news',
      category: 'content',
      template: 'Here are this month\'s highlights and important updates...'
    },
    {
      id: 'sales-outreach',
      name: 'Sales Outreach',
      description: 'Initial sales contact emails',
      category: 'sales',
      template: 'I noticed your company is working on [specific project]...'
    },
    {
      id: 'thank-you',
      name: 'Thank You',
      description: 'Express gratitude to customers',
      category: 'relationship',
      template: 'Thank you for your recent purchase and continued trust in our brand...'
    }
  ])

  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [generatedSubject, setGeneratedSubject] = useState('')
  const [generationHistory, setGenerationHistory] = useState<GenerationHistory[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState<string>('')
  const [bulkGeneration, setBulkGeneration] = useState(false)
  const [bulkCount, setBulkCount] = useState(3)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Load generation history
  useEffect(() => {
    if (session) {
      loadGenerationHistory()
    }
  }, [session])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const loadGenerationHistory = async () => {
    try {
      const response = await fetch('/api/content?type=email&limit=10')
      if (response.ok) {
        const data = await response.json()
        setGenerationHistory(data.content || [])
      }
    } catch (error) {
      console.error('Failed to load generation history:', error)
    }
  }

  const handleGenerate = async () => {
    if (!formData.purpose || !formData.audience) {
      alert('Purpose and audience are required')
      return
    }

    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/email-generator/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          emailType,
          keyPoints: formData.keyPoints.filter(point => point.trim() !== ''),
          bulkGeneration,
          bulkCount: bulkGeneration ? bulkCount : 1,
          template: selectedTemplate
        })
      })
      
      const data = await response.json()
      if (data.success) {
        if (bulkGeneration) {
          // Handle bulk generation results
          setGeneratedContent(data.emails.map((email: any, index: number) => 
            `=== EMAIL ${index + 1} ===\n\nSubject: ${email.subject}\n\n${email.content}`
          ).join('\n\n' + '='.repeat(50) + '\n\n'))
        } else {
          setGeneratedContent(data.content)
          if (data.subject) {
            setGeneratedSubject(data.subject)
          }
        }
        await loadGenerationHistory()
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      alert('Failed to generate email')
    } finally {
      setIsGenerating(false)
    }
  }

  const addKeyPoint = () => {
    setFormData({...formData, keyPoints: [...formData.keyPoints, '']})
  }

  const removeKeyPoint = (index: number) => {
    const newKeyPoints = formData.keyPoints.filter((_, i) => i !== index)
    setFormData({...formData, keyPoints: newKeyPoints.length > 0 ? newKeyPoints : ['']})
  }

  const updateKeyPoint = (index: number, value: string) => {
    const newKeyPoints = [...formData.keyPoints]
    newKeyPoints[index] = value
    setFormData({...formData, keyPoints: newKeyPoints})
  }

  const loadTemplate = (template: EmailTemplate) => {
    setFormData({
      ...formData,
      purpose: template.name,
      keyPoints: [template.description, ...formData.keyPoints.slice(1)]
    })
    setSelectedTemplate(template.id)
    setEmailType(template.category)
  }

  const copyToClipboard = () => {
    const content = generatedSubject ? `Subject: ${generatedSubject}\n\n${generatedContent}` : generatedContent
    navigator.clipboard.writeText(content)
  }

  const downloadAsText = () => {
    const content = generatedSubject ? `Subject: ${generatedSubject}\n\n${generatedContent}` : generatedContent
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `email-${formData.purpose.replace(/\s+/g, '-').toLowerCase()}.txt`
    a.click()
  }

  const sendToGmailComposer = () => {
    // Navigate to Gmail automate page with pre-filled data
    const params = new URLSearchParams({
      subject: generatedSubject,
      body: generatedContent,
      prefill: 'true'
    })
    router.push(`/email-generator/automate?${params.toString()}`)
  }

  const emailTypes = [
    { id: 'marketing', label: 'Marketing', icon: Target },
    { id: 'sales', label: 'Sales', icon: Users },
    { id: 'business', label: 'Business', icon: FileText },
    { id: 'onboarding', label: 'Onboarding', icon: Calendar },
    { id: 'content', label: 'Content', icon: BookOpen },
    { id: 'relationship', label: 'Relationship', icon: Mail }
  ]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-emerald-900/20 to-slate-900" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-emerald-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-teal-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/email-generator">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-emerald-600 to-teal-600">
                    <PenTool className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">AI Email Generator</h1>
                    <p className="text-gray-400">Powered by Gemini 2.5 Flash Lite</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          {/* Tabs */}
          <div className="flex space-x-1 bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-1 mb-8">
            {[
              { id: 'generate', label: 'Generate', icon: PenTool },
              { id: 'templates', label: 'Templates', icon: FileText },
              { id: 'history', label: 'History', icon: RefreshCw }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-xl font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </motion.button>
            ))}
          </div>

          {/* Generate Tab */}
          {activeTab === 'generate' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Configuration Panel */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                    <Sparkles className="w-5 h-5 mr-2 text-emerald-400" />
                    Email Configuration
                  </h2>

                  {/* Email Type Selection */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-3">Email Type</label>
                    <div className="grid grid-cols-2 gap-2">
                      {emailTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          onClick={() => setEmailType(type.id)}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border transition-all flex items-center space-x-2 ${
                            emailType === type.id
                              ? 'bg-gradient-to-r from-emerald-600 to-teal-600 text-white border-emerald-500'
                              : 'bg-white/10 text-gray-300 border-white/20 hover:bg-white/20'
                          }`}
                        >
                          <type.icon className="w-4 h-4" />
                          <span className="text-sm">{type.label}</span>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Purpose */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email Purpose *
                    </label>
                    <input
                      type="text"
                      value={formData.purpose}
                      onChange={(e) => setFormData({...formData, purpose: e.target.value})}
                      placeholder="e.g., Product launch announcement, Welcome new customers..."
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    />
                  </div>

                  {/* Audience */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Target Audience *
                    </label>
                    <input
                      type="text"
                      value={formData.audience}
                      onChange={(e) => setFormData({...formData, audience: e.target.value})}
                      placeholder="e.g., Existing customers, Potential leads, Team members..."
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    />
                  </div>

                  {/* Advanced Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {/* Tone */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Tone</label>
                      <select
                        value={formData.tone}
                        onChange={(e) => setFormData({...formData, tone: e.target.value})}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                      >
                        <option value="professional">Professional</option>
                        <option value="friendly">Friendly</option>
                        <option value="formal">Formal</option>
                        <option value="casual">Casual</option>
                        <option value="persuasive">Persuasive</option>
                        <option value="urgent">Urgent</option>
                        <option value="grateful">Grateful</option>
                        <option value="excited">Excited</option>
                      </select>
                    </div>

                    {/* Length */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Length</label>
                      <select
                        value={formData.length}
                        onChange={(e) => setFormData({...formData, length: e.target.value})}
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                      >
                        <option value="short">Short (1-2 paragraphs)</option>
                        <option value="medium">Medium (3-4 paragraphs)</option>
                        <option value="long">Long (5+ paragraphs)</option>
                      </select>
                    </div>
                  </div>

                  {/* Context */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Additional Context
                    </label>
                    <textarea
                      value={formData.context}
                      onChange={(e) => setFormData({...formData, context: e.target.value})}
                      placeholder="Any additional context, background information, or specific requirements..."
                      rows={3}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all resize-none"
                    />
                  </div>

                  {/* Key Points */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Key Points to Include
                    </label>
                    <div className="space-y-3">
                      {formData.keyPoints.map((point, index) => (
                        <div key={index} className="flex space-x-2">
                          <input
                            type="text"
                            value={point}
                            onChange={(e) => updateKeyPoint(index, e.target.value)}
                            placeholder={`Key point ${index + 1}...`}
                            className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                          />
                          {formData.keyPoints.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeKeyPoint(index)}
                              className="p-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all"
                            >
                              <X className="w-5 h-5" />
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addKeyPoint}
                        className="w-full py-3 border-2 border-dashed border-white/20 rounded-xl text-gray-400 hover:text-white hover:border-emerald-500/50 transition-all flex items-center justify-center space-x-2"
                      >
                        <Plus className="w-5 h-5" />
                        <span>Add Key Point</span>
                      </button>
                    </div>
                  </div>

                  {/* Call to Action */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Call to Action
                    </label>
                    <input
                      type="text"
                      value={formData.callToAction}
                      onChange={(e) => setFormData({...formData, callToAction: e.target.value})}
                      placeholder="e.g., Visit our website, Schedule a demo, Sign up today..."
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    />
                  </div>

                  {/* Bulk Generation Option */}
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-3">
                      <input
                        type="checkbox"
                        id="bulkGeneration"
                        checked={bulkGeneration}
                        onChange={(e) => setBulkGeneration(e.target.checked)}
                        className="w-4 h-4 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500"
                      />
                      <label htmlFor="bulkGeneration" className="text-sm font-medium text-gray-300">
                        Bulk Generation
                      </label>
                    </div>
                    {bulkGeneration && (
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          Number of Variations
                        </label>
                        <select
                          value={bulkCount}
                          onChange={(e) => setBulkCount(parseInt(e.target.value))}
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                        >
                          <option value={3}>3 variations</option>
                          <option value={5}>5 variations</option>
                          <option value={10}>10 variations</option>
                        </select>
                      </div>
                    )}
                  </div>

                  {/* Options */}
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="includeSubject"
                        checked={formData.includeSubject}
                        onChange={(e) => setFormData({...formData, includeSubject: e.target.checked})}
                        className="w-4 h-4 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500"
                      />
                      <label htmlFor="includeSubject" className="text-sm text-gray-300">Include Subject Line</label>
                    </div>
                  </div>

                  {/* Generate Button */}
                  <motion.button
                    onClick={handleGenerate}
                    disabled={isGenerating || !formData.purpose || !formData.audience}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Generating Email...</span>
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-5 h-5" />
                        <span>Generate Email</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </motion.div>

              {/* Output Panel */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold text-white">Generated Email</h2>
                    {generatedContent && (
                      <div className="flex space-x-2">
                        <button
                          onClick={copyToClipboard}
                          className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                          title="Copy to clipboard"
                        >
                          <Copy className="w-4 h-4 text-white" />
                        </button>
                        <button
                          onClick={downloadAsText}
                          className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                          title="Download as text"
                        >
                          <Download className="w-4 h-4 text-white" />
                        </button>
                        <button
                          onClick={sendToGmailComposer}
                          className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border border-blue-500/20 rounded-lg transition-colors"
                          title="Send via Gmail"
                        >
                          <Send className="w-4 h-4 text-white" />
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {generatedContent ? (
                    <div className="space-y-4">
                      {generatedSubject && (
                        <div className="bg-black/40 rounded-xl p-4 border border-white/10">
                          <div className="text-emerald-400 font-medium mb-2">Subject Line:</div>
                          <div className="text-white">{generatedSubject}</div>
                        </div>
                      )}
                      <div className="bg-black/40 rounded-xl p-4 border border-white/10 max-h-[600px] overflow-y-auto">
                        <pre className="text-gray-300 whitespace-pre-wrap font-mono text-sm leading-relaxed">
                          {generatedContent}
                        </pre>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12 text-gray-400">
                      <PenTool className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>Your AI-generated email will appear here...</p>
                      <p className="text-sm mt-2">Configure your email settings and click "Generate Email"</p>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          )}

          {/* Templates Tab */}
          {activeTab === 'templates' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              {templates.map((template) => (
                <motion.div
                  key={template.id}
                  whileHover={{ y: -5 }}
                  className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all cursor-pointer"
                  onClick={() => loadTemplate(template)}
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-600 to-teal-600">
                      <FileText className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                      <span className="text-xs text-emerald-400 bg-emerald-500/10 px-2 py-1 rounded-full">
                        {template.category}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm mb-4">{template.description}</p>
                  <div className="text-gray-400 text-xs bg-black/40 rounded-lg p-3 border border-white/10">
                    {template.template.substring(0, 100)}...
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="mt-4 w-full py-2 bg-gradient-to-r from-emerald-600 to-teal-600 text-white text-sm font-medium rounded-lg hover:from-emerald-700 hover:to-teal-700 transition-all"
                  >
                    Use Template
                  </motion.button>
                </motion.div>
              ))}
            </motion.div>
          )}

          {/* History Tab */}
          {activeTab === 'history' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white">Generation History</h2>
                <motion.button
                  onClick={loadGenerationHistory}
                  whileHover={{ scale: 1.05 }}
                  className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4 text-white" />
                </motion.button>
              </div>

              <div className="space-y-4">
                {generationHistory.map((item) => (
                  <div key={item.id} className="p-4 bg-white/5 rounded-xl border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-white font-medium">{item.subject || item.type}</h3>
                      <span className="text-gray-400 text-sm">
                        {new Date(item.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm line-clamp-3">
                      {item.content.substring(0, 200)}...
                    </p>
                    <div className="flex space-x-2 mt-3">
                      <button
                        onClick={() => {
                          setGeneratedContent(item.content)
                          setActiveTab('generate')
                        }}
                        className="px-3 py-1 bg-emerald-600/20 text-emerald-400 text-sm rounded-lg hover:bg-emerald-600/30 transition-colors"
                      >
                        View
                      </button>
                      <button
                        onClick={() => navigator.clipboard.writeText(item.content)}
                        className="px-3 py-1 bg-white/10 text-gray-300 text-sm rounded-lg hover:bg-white/20 transition-colors"
                      >
                        Copy
                      </button>
                    </div>
                  </div>
                ))}

                {generationHistory.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    <RefreshCw className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No generation history found</p>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  )
}