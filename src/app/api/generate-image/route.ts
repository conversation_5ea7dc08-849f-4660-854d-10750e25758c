import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { IdeogramService } from '@/lib/ideogram'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { headingText, context, blogTitle } = await request.json()

    if (!headingText) {
      return NextResponse.json(
        { error: 'Heading text is required' },
        { status: 400 }
      )
    }

    console.log(`🎨 Generating image for heading: "${headingText}"`)

    // Initialize Ideogram service with API key
    const ideogramService = new IdeogramService('Qj6s6Yym5Yi6-oXYBHsMThfSv7X0EEOOtuRDXrhM1gHIHv1vLRLNNWRbLO5OA22flhHQh8MwMNcnU2pxXsNuxg')

    // Generate image for the heading with enhanced prompts
    const imageUrl = await ideogramService.generateBlogHeadingImage(
      headingText,
      context,
      blogTitle
    )

    console.log(`✅ Image generated successfully: ${imageUrl}`)

    return NextResponse.json({
      success: true,
      imageUrl,
      headingText
    })

  } catch (error) {
    console.error('❌ Image generation failed:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Image generation failed'
    
    return NextResponse.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    )
  }
}
