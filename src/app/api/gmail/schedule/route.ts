import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { createGmailService, isValidEmail, parseEmailAddresses } from '@/lib/gmail'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (!session.accessToken) {
      return NextResponse.json(
        { error: 'Gmail access not granted. Please authenticate with Gmail first.' },
        { status: 403 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'emails')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Email quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { to, cc, bcc, subject, body, isHtml, scheduleTime } = await request.json()

    // Validate required fields
    if (!to || !subject || !body || !scheduleTime) {
      return NextResponse.json(
        { error: 'To, subject, body, and scheduleTime are required' },
        { status: 400 }
      )
    }

    // Validate schedule time
    const scheduledDate = new Date(scheduleTime)
    if (isNaN(scheduledDate.getTime()) || scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: 'Schedule time must be a valid future date' },
        { status: 400 }
      )
    }

    // Parse and validate email addresses
    const toAddresses = Array.isArray(to) ? to : parseEmailAddresses(to)
    const ccAddresses = cc ? (Array.isArray(cc) ? cc : parseEmailAddresses(cc)) : []
    const bccAddresses = bcc ? (Array.isArray(bcc) ? bcc : parseEmailAddresses(bcc)) : []

    // Validate email addresses
    const invalidEmails = [...toAddresses, ...ccAddresses, ...bccAddresses]
      .filter(email => !isValidEmail(email))

    if (invalidEmails.length > 0) {
      return NextResponse.json(
        { error: `Invalid email addresses: ${invalidEmails.join(', ')}` },
        { status: 400 }
      )
    }

    if (toAddresses.length === 0) {
      return NextResponse.json(
        { error: 'At least one valid recipient is required' },
        { status: 400 }
      )
    }

    // Test Gmail connection (to ensure we can send when the time comes)
    const gmailService = createGmailService(session.accessToken, session.refreshToken)
    const connectionTest = await gmailService.checkConnection()
    
    if (!connectionTest.success) {
      return NextResponse.json(
        { error: 'Gmail connection failed. Please re-authenticate.' },
        { status: 403 }
      )
    }

    console.log('📅 Scheduling email for:', scheduledDate.toISOString())

    // Save scheduled email to database
    const scheduledEmail = await prisma.content.create({
      data: {
        userId: session.user.id,
        type: 'email_scheduled',
        title: `Scheduled: ${subject}`,
        content: body,
        metadata: JSON.stringify({
          to: toAddresses,
          cc: ccAddresses,
          bcc: bccAddresses,
          isHtml: isHtml || false,
          scheduleTime: scheduledDate.toISOString(),
          status: 'pending',
          createdAt: new Date().toISOString()
        })
      }
    })

    // Use quota (reserve it for when the email is sent)
    const quotaUsed = await QuotaManager.useQuota(session.user.id, 'emails')
    if (!quotaUsed) {
      console.error('Failed to update quota after scheduling email')
    }

    console.log('✅ Email scheduled successfully:', scheduledEmail.id)

    return NextResponse.json({
      success: true,
      scheduleId: scheduledEmail.id,
      scheduleTime: scheduledDate.toISOString(),
      message: 'Email scheduled successfully',
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      }
    })

  } catch (error) {
    console.error('Gmail schedule error:', error)
    return NextResponse.json(
      { error: 'Failed to schedule email' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get scheduled emails for the user
    const scheduledEmails = await prisma.content.findMany({
      where: {
        userId: session.user.id,
        type: 'email_scheduled'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50
    })

    const emails = scheduledEmails.map(email => {
      const metadata = JSON.parse(email.metadata || '{}')
      return {
        id: email.id,
        subject: email.title.replace('Scheduled: ', ''),
        body: email.content,
        to: metadata.to || [],
        cc: metadata.cc || [],
        bcc: metadata.bcc || [],
        scheduleTime: metadata.scheduleTime,
        status: metadata.status || 'pending',
        createdAt: email.createdAt,
        isHtml: metadata.isHtml || false
      }
    })

    return NextResponse.json({
      success: true,
      emails,
      count: emails.length
    })

  } catch (error) {
    console.error('Get scheduled emails error:', error)
    return NextResponse.json(
      { error: 'Failed to get scheduled emails' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const scheduleId = searchParams.get('id')

    if (!scheduleId) {
      return NextResponse.json(
        { error: 'Schedule ID is required' },
        { status: 400 }
      )
    }

    // Find and delete the scheduled email
    const scheduledEmail = await prisma.content.findFirst({
      where: {
        id: scheduleId,
        userId: session.user.id,
        type: 'email_scheduled'
      }
    })

    if (!scheduledEmail) {
      return NextResponse.json(
        { error: 'Scheduled email not found' },
        { status: 404 }
      )
    }

    // Check if it's still pending
    const metadata = JSON.parse(scheduledEmail.metadata || '{}')
    if (metadata.status !== 'pending') {
      return NextResponse.json(
        { error: `Cannot cancel email with status: ${metadata.status}` },
        { status: 400 }
      )
    }

    // Delete the scheduled email
    await prisma.content.delete({
      where: {
        id: scheduleId
      }
    })

    console.log('✅ Scheduled email cancelled:', scheduleId)

    return NextResponse.json({
      success: true,
      message: 'Scheduled email cancelled successfully'
    })

  } catch (error) {
    console.error('Cancel scheduled email error:', error)
    return NextResponse.json(
      { error: 'Failed to cancel scheduled email' },
      { status: 500 }
    )
  }
}