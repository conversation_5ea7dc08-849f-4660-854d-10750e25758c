import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { createGmailService } from '@/lib/gmail'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (!session.accessToken) {
      return NextResponse.json(
        { error: 'Gmail access not granted. Please authenticate with Gmail first.' },
        { status: 403 }
      )
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const maxResults = parseInt(searchParams.get('maxResults') || '10')
    const query = searchParams.get('query') || ''

    // Create Gmail service
    const gmailService = createGmailService(session.accessToken, session.refreshToken)

    console.log('📧 Fetching emails from Gmail...')
    
    let emailsResult
    if (query.trim()) {
      // Search emails with query
      emailsResult = await gmailService.searchEmails(query, Math.min(maxResults, 50))
    } else {
      // Get recent emails
      emailsResult = await gmailService.getRecentEmails(Math.min(maxResults, 50))
    }

    if (!emailsResult.success) {
      return NextResponse.json(
        { error: `Failed to fetch emails: ${emailsResult.error}` },
        { status: 500 }
      )
    }

    console.log(`✅ Retrieved ${emailsResult.emails?.length || 0} emails successfully`)

    return NextResponse.json({
      success: true,
      emails: emailsResult.emails || [],
      count: emailsResult.emails?.length || 0,
      query: query || 'recent'
    })

  } catch (error) {
    console.error('Gmail messages fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch emails' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (!session.accessToken) {
      return NextResponse.json(
        { error: 'Gmail access not granted. Please authenticate with Gmail first.' },
        { status: 403 }
      )
    }

    const { action, messageId } = await request.json()

    if (!action || !messageId) {
      return NextResponse.json(
        { error: 'Action and messageId are required' },
        { status: 400 }
      )
    }

    // Create Gmail service
    const gmailService = createGmailService(session.accessToken, session.refreshToken)

    console.log(`📧 Performing action "${action}" on message ${messageId}`)

    let result
    switch (action) {
      case 'markAsRead':
        result = await gmailService.markAsRead(messageId)
        break
      default:
        return NextResponse.json(
          { error: `Unsupported action: ${action}` },
          { status: 400 }
        )
    }

    if (!result.success) {
      return NextResponse.json(
        { error: `Failed to ${action}: ${result.error}` },
        { status: 500 }
      )
    }

    console.log(`✅ Action "${action}" completed successfully`)

    return NextResponse.json({
      success: true,
      message: `${action} completed successfully`
    })

  } catch (error) {
    console.error('Gmail message action error:', error)
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    )
  }
}