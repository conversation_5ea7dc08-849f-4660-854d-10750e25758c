import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { createGmailService, isValidEmail, parseEmailAddresses } from '@/lib/gmail'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (!session.accessToken) {
      return NextResponse.json(
        { error: 'Gmail access not granted. Please authenticate with Gmail first.' },
        { status: 403 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'emails')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Email quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { to, cc, bcc, subject, body, isHtml } = await request.json()

    // Validate required fields
    if (!to || !subject || !body) {
      return NextResponse.json(
        { error: 'To, subject, and body are required' },
        { status: 400 }
      )
    }

    // Parse and validate email addresses
    const toAddresses = Array.isArray(to) ? to : parseEmailAddresses(to)
    const ccAddresses = cc ? (Array.isArray(cc) ? cc : parseEmailAddresses(cc)) : []
    const bccAddresses = bcc ? (Array.isArray(bcc) ? bcc : parseEmailAddresses(bcc)) : []

    // Validate email addresses
    const invalidEmails = [...toAddresses, ...ccAddresses, ...bccAddresses]
      .filter(email => !isValidEmail(email))

    if (invalidEmails.length > 0) {
      return NextResponse.json(
        { error: `Invalid email addresses: ${invalidEmails.join(', ')}` },
        { status: 400 }
      )
    }

    if (toAddresses.length === 0) {
      return NextResponse.json(
        { error: 'At least one valid recipient is required' },
        { status: 400 }
      )
    }

    // Create Gmail service and send email
    const gmailService = createGmailService(session.accessToken, session.refreshToken)
    
    console.log('📧 Sending email via Gmail API...')
    const sendResult = await gmailService.sendEmail({
      to: toAddresses,
      cc: ccAddresses.length > 0 ? ccAddresses : undefined,
      bcc: bccAddresses.length > 0 ? bccAddresses : undefined,
      subject,
      body,
      isHtml: isHtml || false
    })

    if (!sendResult.success) {
      return NextResponse.json(
        { error: `Failed to send email: ${sendResult.error}` },
        { status: 500 }
      )
    }

    console.log('✅ Email sent successfully via Gmail')

    // Use quota
    const quotaUsed = await QuotaManager.useQuota(session.user.id, 'emails')
    if (!quotaUsed) {
      console.error('Failed to update quota after successful email send')
    }

    // Save email to database for history
    try {
      await prisma.content.create({
        data: {
          userId: session.user.id,
          type: 'email_sent',
          title: `Sent: ${subject}`,
          content: body,
          metadata: JSON.stringify({
            to: toAddresses,
            cc: ccAddresses,
            bcc: bccAddresses,
            isHtml,
            messageId: sendResult.messageId,
            sentAt: new Date().toISOString(),
            method: 'gmail_api'
          })
        }
      })
    } catch (dbError) {
      console.error('Failed to save sent email to database:', dbError)
      // Don't fail the request if saving fails
    }

    return NextResponse.json({
      success: true,
      messageId: sendResult.messageId,
      message: 'Email sent successfully',
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      }
    })

  } catch (error) {
    console.error('Gmail send error:', error)
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    )
  }
}