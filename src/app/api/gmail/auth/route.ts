import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { createGmailService } from '@/lib/gmail'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    if (!session.accessToken) {
      return NextResponse.json(
        { error: 'Gmail access not granted. Please re-authenticate with Gmail permissions.' },
        { status: 403 }
      )
    }

    // Test Gmail connection
    const gmailService = createGmailService(session.accessToken, session.refreshToken)
    const connectionResult = await gmailService.checkConnection()

    if (!connectionResult.success) {
      return NextResponse.json(
        { error: 'Gmail connection failed. Please re-authenticate.' },
        { status: 403 }
      )
    }

    // Get Gmail profile
    const profileResult = await gmailService.getProfile()
    if (!profileResult.success) {
      return NextResponse.json(
        { error: 'Failed to get Gmail profile' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      connected: true,
      profile: profileResult.profile
    })

  } catch (error) {
    console.error('Gmail auth check error:', error)
    return NextResponse.json(
      { error: 'Failed to check Gmail authentication' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // This endpoint can be used to trigger re-authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Return the OAuth URL for re-authentication
    const redirectUri = `${process.env.NEXTAUTH_URL}/api/auth/callback/google`
    const scope = 'openid email profile https://www.googleapis.com/auth/gmail.send https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.compose https://www.googleapis.com/auth/gmail.modify'
    
    const authUrl = `https://accounts.google.com/o/oauth2/auth?` +
      `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `scope=${encodeURIComponent(scope)}&` +
      `response_type=code&` +
      `access_type=offline&` +
      `prompt=consent`

    return NextResponse.json({
      success: true,
      authUrl
    })

  } catch (error) {
    console.error('Gmail re-auth error:', error)
    return NextResponse.json(
      { error: 'Failed to generate authentication URL' },
      { status: 500 }
    )
  }
}