import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'emails')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Email quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { 
      purpose, 
      audience, 
      tone, 
      keyPoints, 
      context,
      callToAction,
      brandVoice,
      length,
      urgency,
      personalization,
      includeSubject,
      emailType,
      bulkGeneration,
      bulkCount,
      template
    } = await request.json()

    if (!purpose || !audience) {
      return NextResponse.json(
        { error: 'Purpose and audience are required' },
        { status: 400 }
      )
    }

    const gemini = new GeminiService()
    
    console.log('📧 Generating enhanced email with Gemini 2.5 Flash Lite...')

    if (bulkGeneration && bulkCount > 1) {
      // Generate multiple email variations
      const emails = []
      
      for (let i = 0; i < Math.min(bulkCount, 10); i++) {
        console.log(`📧 Generating email variation ${i + 1}/${bulkCount}...`)
        
        const emailContent = await generateAdvancedEmail(gemini, {
          purpose,
          audience,
          tone,
          keyPoints: keyPoints || [],
          context,
          callToAction,
          brandVoice,
          length: length || 'medium',
          urgency: urgency || 'normal',
          personalization,
          includeSubject: includeSubject !== false,
          emailType: emailType || 'marketing',
          template,
          variationIndex: i + 1
        })

        emails.push(emailContent)
      }

      console.log(`✅ ${emails.length} email variations generated successfully`)

      // Use quota for bulk generation
      const quotaUsed = await QuotaManager.useQuota(session.user.id, 'emails')
      if (!quotaUsed) {
        console.error('Failed to update quota after successful bulk generation')
      }

      // Save bulk generation to database
      try {
        await prisma.content.create({
          data: {
            userId: session.user.id,
            type: 'email_bulk',
            title: `Bulk: ${purpose} (${emails.length} variations)`,
            content: emails.map((email, index) => 
              `=== EMAIL ${index + 1} ===\n\nSubject: ${email.subject}\n\n${email.content}`
            ).join('\n\n' + '='.repeat(50) + '\n\n'),
            tone: tone || 'professional',
            metadata: JSON.stringify({
              purpose,
              audience,
              emailType,
              bulkCount: emails.length,
              keyPoints,
              context,
              callToAction,
              generatedAt: new Date().toISOString()
            })
          }
        })
      } catch (dbError) {
        console.error('Failed to save bulk content to database:', dbError)
      }

      return NextResponse.json({
        success: true,
        emails: emails,
        count: emails.length,
        quota: {
          used: quotaCheck.used + 1,
          limit: quotaCheck.limit,
          remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
        }
      })
    } else {
      // Generate single email
      const emailContent = await generateAdvancedEmail(gemini, {
        purpose,
        audience,
        tone,
        keyPoints: keyPoints || [],
        context,
        callToAction,
        brandVoice,
        length: length || 'medium',
        urgency: urgency || 'normal',
        personalization,
        includeSubject: includeSubject !== false,
        emailType: emailType || 'marketing',
        template
      })

      console.log('✅ Enhanced email generated successfully')

      // Use quota
      const quotaUsed = await QuotaManager.useQuota(session.user.id, 'emails')
      if (!quotaUsed) {
        console.error('Failed to update quota after successful generation')
      }

      // Save content to database
      try {
        await prisma.content.create({
          data: {
            userId: session.user.id,
            type: 'email_enhanced',
            title: `Enhanced: ${purpose}`,
            content: emailContent.content,
            tone: tone || 'professional',
            metadata: JSON.stringify({
              purpose,
              audience,
              emailType,
              subject: emailContent.subject,
              keyPoints,
              context,
              callToAction,
              generatedAt: new Date().toISOString()
            })
          }
        })
      } catch (dbError) {
        console.error('Failed to save content to database:', dbError)
      }

      return NextResponse.json({
        success: true,
        content: emailContent.content,
        subject: emailContent.subject,
        quota: {
          used: quotaCheck.used + 1,
          limit: quotaCheck.limit,
          remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
        }
      })
    }

  } catch (error) {
    console.error('Enhanced email generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate email' },
      { status: 500 }
    )
  }
}

async function generateAdvancedEmail(
  gemini: GeminiService,
  params: {
    purpose: string
    audience: string
    tone: string
    keyPoints: string[]
    context?: string
    callToAction?: string
    brandVoice?: string
    length: string
    urgency: string
    personalization?: string
    includeSubject: boolean
    emailType: string
    template?: string
    variationIndex?: number
  }
): Promise<{ content: string; subject?: string }> {
  
  const lengthInstructions = {
    short: '1-2 concise paragraphs (150-300 words)',
    medium: '3-4 well-structured paragraphs (300-500 words)',
    long: '5+ comprehensive paragraphs (500+ words)'
  }

  const urgencyInstructions = {
    low: 'casual pacing with gentle persuasion',
    normal: 'professional pacing with clear messaging',
    high: 'urgent tone with immediate action required',
    critical: 'critical urgency requiring immediate attention'
  }

  const emailTypeInstructions = {
    marketing: 'promotional content focused on products/services with compelling benefits',
    sales: 'persuasive sales-focused content with clear value propositions',
    business: 'professional business communication with formal structure',
    onboarding: 'welcoming and educational content for new users/customers',
    content: 'informative and valuable content sharing insights or updates',
    relationship: 'relationship-building content focused on connection and trust'
  }

  let prompt = `
You are an expert email marketing specialist and copywriter with years of experience creating high-converting emails. Create a professional, engaging email using the advanced specifications below.

EMAIL SPECIFICATIONS:
- Purpose: ${params.purpose}
- Type: ${params.emailType} (${emailTypeInstructions[params.emailType as keyof typeof emailTypeInstructions] || 'general email communication'})
- Target Audience: ${params.audience}
- Tone: ${params.tone}
- Length: ${lengthInstructions[params.length as keyof typeof lengthInstructions]}
- Urgency Level: ${params.urgency} (${urgencyInstructions[params.urgency as keyof typeof urgencyInstructions]})

${params.keyPoints.length > 0 ? `KEY POINTS TO INCLUDE:
${params.keyPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}` : ''}

${params.context ? `ADDITIONAL CONTEXT:
${params.context}` : ''}

${params.callToAction ? `CALL TO ACTION:
${params.callToAction}` : ''}

${params.brandVoice ? `BRAND VOICE GUIDELINES:
${params.brandVoice}` : ''}

${params.personalization ? `PERSONALIZATION NOTES:
${params.personalization}` : ''}

${params.template ? `TEMPLATE REFERENCE:
Use this as inspiration but create original content: ${params.template}` : ''}

${params.variationIndex ? `VARIATION REQUIREMENT:
This is variation #${params.variationIndex}. Make it unique and different from other variations while maintaining the same core message and purpose.` : ''}

ADVANCED REQUIREMENTS:
- Start with a compelling hook that grabs attention immediately
- Use psychological triggers appropriate for ${params.emailType} emails
- Include social proof elements where relevant
- Create emotional connection with the audience
- Use persuasive writing techniques (urgency, scarcity, benefit-focused language)
- Optimize for mobile reading with shorter paragraphs
- Include clear visual breaks and scannable content
- End with a strong, actionable conclusion
- Use active voice and power words
- Maintain consistent brand voice throughout

STRUCTURE REQUIREMENTS:
1. Attention-grabbing opening line
2. Value proposition or main message
3. Supporting details with benefits
4. Social proof or credibility elements (if applicable)
5. Clear call-to-action
6. Professional closing

${params.includeSubject ? `
Please provide both a compelling subject line and the email content.

Format your response as:
SUBJECT: [Your compelling subject line]

EMAIL:
[Your email content]` : `
Please provide only the email content (no subject line needed).`}

Create content that not only informs but also drives action and engagement. This should be professional, conversion-optimized content that stands out in crowded inboxes.
`

  const result = await gemini.generateContentWithThinking(
    prompt,
    2048, // Use substantial thinking for better email quality
    false, // Don't include thoughts in response
    {
      temperature: 0.8, // Higher creativity for marketing content
      maxOutputTokens: 2000
    },
    `Enhanced Email Generation - ${params.emailType}`
  )

  if (params.includeSubject) {
    const lines = result.response.split('\n').filter(line => line.trim())
    let subject = ''
    let content = result.response

    // Extract subject line if present
    const subjectMatch = result.response.match(/SUBJECT:\s*(.+)/i)
    if (subjectMatch) {
      subject = subjectMatch[1].trim()
      content = result.response.replace(/SUBJECT:\s*.+/i, '').replace(/EMAIL:\s*/i, '').trim()
    }

    return {
      content,
      subject: subject || undefined
    }
  }

  return {
    content: result.response
  }
}