import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { prompt } = await request.json()

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      )
    }

    console.log('🧠 Generating enhanced prompt with Gemini...')

    // Initialize Gemini service
    const gemini = new GeminiService()

    // Generate enhanced prompt
    const result = await gemini.generateContent(prompt, {
      temperature: 0.8, // Higher creativity for image prompts
      maxOutputTokens: 1000,
      thinkingConfig: {
        thinkingBudget: 0, // No thinking needed for prompt generation
        includeThoughts: false
      }
    })

    const enhancedPrompt = result.response.trim()

    console.log('✅ Enhanced prompt generated successfully')

    return NextResponse.json({
      success: true,
      prompt: enhancedPrompt
    })

  } catch (error) {
    console.error('❌ Gemini prompt generation failed:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Prompt generation failed'
    
    return NextResponse.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    )
  }
}
