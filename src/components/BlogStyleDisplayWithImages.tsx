'use client'

import { useState, useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Copy, Download, Edit, Eye, CheckCircle, Save, X, Library, ExternalLink,
  Image as ImageIcon, Sparkles, Loader2, AlertCircle
} from 'lucide-react'
import Link from 'next/link'

// Component for headings with image generation capability
interface HeadingWithImageGenerationProps {
  level: number
  children: React.ReactNode
  showImageGeneration: boolean
  generatedImages: Record<string, GeneratedImage>
  onGenerateImage: (headingText: string) => void
}

function HeadingWithImageGeneration({
  level,
  children,
  showImageGeneration,
  generatedImages,
  onGenerateImage
}: HeadingWithImageGenerationProps) {
  const headingText = typeof children === 'string' ? children : children?.toString() || ''
  const headingId = headingText.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
  const generatedImage = generatedImages[headingId]

  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements

  return (
    <div className="heading-with-image-container">
      <div className="flex items-center justify-between group">
        <HeadingTag className={`heading-level-${level} flex-1`}>
          {children}
        </HeadingTag>

        {showImageGeneration && (
          <AnimatePresence>
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              onClick={() => onGenerateImage(headingText)}
              disabled={generatedImage?.isLoading}
              className={`ml-4 p-2 rounded-lg transition-all shadow-lg border backdrop-blur-sm ${
                generatedImage?.isLoading
                  ? 'bg-gray-400/50 text-gray-600 cursor-not-allowed border-gray-300/50'
                  : generatedImage?.imageUrl
                  ? 'bg-emerald-500/80 text-white border-emerald-400/50 hover:bg-emerald-600/90'
                  : 'bg-sky-500/80 text-white border-sky-400/50 hover:bg-sky-600/90'
              } opacity-0 group-hover:opacity-100`}
              title={
                generatedImage?.isLoading
                  ? 'Generating image...'
                  : generatedImage?.imageUrl
                  ? 'Regenerate image'
                  : 'Generate image'
              }
            >
              {generatedImage?.isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : generatedImage?.imageUrl ? (
                <Sparkles className="w-4 h-4" />
              ) : (
                <ImageIcon className="w-4 h-4" />
              )}
            </motion.button>
          </AnimatePresence>
        )}
      </div>

      {/* Generated Image Display */}
      {generatedImage && (
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 mb-6"
          >
            {generatedImage.isLoading && (
              <div className="flex items-center justify-center p-8 bg-white/20 backdrop-blur-sm rounded-xl border border-sky-200/30">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin text-sky-500 mx-auto mb-2" />
                  <p className="text-sm text-sky-600">Generating image...</p>
                </div>
              </div>
            )}

            {generatedImage.error && (
              <div className="flex items-center justify-center p-4 bg-red-100/20 backdrop-blur-sm rounded-xl border border-red-200/30">
                <div className="text-center text-red-600">
                  <AlertCircle className="w-6 h-6 mx-auto mb-2" />
                  <p className="text-sm">{generatedImage.error}</p>
                  <button
                    onClick={() => onGenerateImage(headingText)}
                    className="mt-2 px-3 py-1 bg-red-500/80 text-white rounded-lg hover:bg-red-600/90 transition-all text-xs"
                  >
                    Retry
                  </button>
                </div>
              </div>
            )}

            {generatedImage.imageUrl && !generatedImage.isLoading && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="relative overflow-hidden rounded-xl shadow-lg border border-sky-200/30"
              >
                <img
                  src={generatedImage.imageUrl}
                  alt={`Generated image for: ${headingText}`}
                  className="w-full h-auto object-cover"
                  style={{ maxHeight: '400px' }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>
      )}
    </div>
  )
}

interface BlogStyleDisplayProps {
  content: string
  title?: string
  onContentChange?: (content: string) => void
  editable?: boolean
  className?: string
  savedToDatabase?: boolean
  databaseId?: string
  showLibraryLink?: boolean
}

interface GeneratedImage {
  headingText: string
  imageUrl: string
  isLoading: boolean
  error?: string
}

export default function BlogStyleDisplayWithImages({
  content,
  title,
  onContentChange,
  editable = true,
  className = "",
  savedToDatabase = false,
  databaseId,
  showLibraryLink = true
}: BlogStyleDisplayProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(content)
  const [copied, setCopied] = useState(false)
  const [showImageGeneration, setShowImageGeneration] = useState(false)
  const [generatedImages, setGeneratedImages] = useState<Record<string, GeneratedImage>>({})
  const [headings, setHeadings] = useState<Array<{ text: string; level: number; id: string }>>([])

  useEffect(() => {
    setEditedContent(content)
    extractHeadings(content)
  }, [content])

  // Extract headings from markdown content
  const extractHeadings = (markdownContent: string) => {
    const headingRegex = /^(#{1,6})\s+(.+)$/gm
    const extractedHeadings: Array<{ text: string; level: number; id: string }> = []
    let match

    while ((match = headingRegex.exec(markdownContent)) !== null) {
      const level = match[1].length
      const text = match[2].trim()
      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
      
      extractedHeadings.push({ text, level, id })
    }

    setHeadings(extractedHeadings)
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(isEditing ? editedContent : content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleDownload = () => {
    const contentToDownload = isEditing ? editedContent : content
    const blob = new Blob([contentToDownload], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title || 'blog-post'}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSaveEdit = () => {
    if (onContentChange) {
      onContentChange(editedContent)
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditedContent(content)
    setIsEditing(false)
  }

  const generateImageForHeading = async (headingText: string) => {
    const headingId = headingText.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    
    // Set loading state
    setGeneratedImages(prev => ({
      ...prev,
      [headingId]: {
        headingText,
        imageUrl: '',
        isLoading: true
      }
    }))

    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          headingText,
          context: content.substring(0, 500),
          blogTitle: title
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate image')
      }

      // Update with generated image
      setGeneratedImages(prev => ({
        ...prev,
        [headingId]: {
          headingText,
          imageUrl: data.imageUrl,
          isLoading: false
        }
      }))

    } catch (error) {
      console.error('Failed to generate image:', error)
      
      // Update with error state
      setGeneratedImages(prev => ({
        ...prev,
        [headingId]: {
          headingText,
          imageUrl: '',
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to generate image'
        }
      }))
    }
  }

  // Custom renderer for headings with image generation
  const customRenderers = {
    h1: ({ children, ...props }: any) => (
      <HeadingWithImageGeneration 
        level={1} 
        children={children} 
        showImageGeneration={showImageGeneration}
        generatedImages={generatedImages}
        onGenerateImage={generateImageForHeading}
        {...props} 
      />
    ),
    h2: ({ children, ...props }: any) => (
      <HeadingWithImageGeneration 
        level={2} 
        children={children} 
        showImageGeneration={showImageGeneration}
        generatedImages={generatedImages}
        onGenerateImage={generateImageForHeading}
        {...props} 
      />
    ),
    h3: ({ children, ...props }: any) => (
      <HeadingWithImageGeneration 
        level={3} 
        children={children} 
        showImageGeneration={showImageGeneration}
        generatedImages={generatedImages}
        onGenerateImage={generateImageForHeading}
        {...props} 
      />
    ),
    h4: ({ children, ...props }: any) => (
      <HeadingWithImageGeneration 
        level={4} 
        children={children} 
        showImageGeneration={showImageGeneration}
        generatedImages={generatedImages}
        onGenerateImage={generateImageForHeading}
        {...props} 
      />
    ),
  }

  return (
    <div className={`sky-bg min-h-screen ${className}`}>
      {/* Toolbar */}
      <div className="bg-white/30 backdrop-blur-xl border-b border-sky-200/50 sticky top-0 z-10 p-4 shadow-lg">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <div>
            {title && <h1 className="text-xl font-bold sky-heading">{title}</h1>}
            <div className="flex items-center space-x-4">
              <p className="text-sm sky-accent">
                {isEditing ? 'Editing Mode' : 'Preview Mode'}
              </p>
              {savedToDatabase && (
                <div className="flex items-center space-x-1 text-xs text-emerald-600 bg-emerald-100/20 px-2 py-1 rounded-lg backdrop-blur-sm">
                  <CheckCircle className="w-3 h-3" />
                  <span>Auto-saved</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Image Generation Toggle */}
            <motion.button
              onClick={() => setShowImageGeneration(!showImageGeneration)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`p-2 rounded-xl transition-all shadow-lg border ${
                showImageGeneration
                  ? 'bg-sky-500/80 text-white border-sky-400/50'
                  : 'bg-white/30 text-sky-700 border-sky-200/50 hover:bg-white/40'
              } backdrop-blur-sm`}
              title={showImageGeneration ? 'Hide image generation' : 'Show image generation'}
            >
              <ImageIcon className="w-4 h-4" />
            </motion.button>

            {editable && (
              <>
                {isEditing ? (
                  <>
                    <motion.button
                      onClick={handleSaveEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-2 bg-emerald-500/80 text-white rounded-xl hover:bg-emerald-600/90 transition-all shadow-lg border border-emerald-400/50 backdrop-blur-sm"
                      title="Save changes"
                    >
                      <Save className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      onClick={handleCancelEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="p-2 bg-red-500/80 text-white rounded-xl hover:bg-red-600/90 transition-all shadow-lg border border-red-400/50 backdrop-blur-sm"
                      title="Cancel editing"
                    >
                      <X className="w-4 h-4" />
                    </motion.button>
                  </>
                ) : (
                  <motion.button
                    onClick={() => setIsEditing(true)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-white/30 text-sky-700 rounded-xl hover:bg-white/40 transition-all shadow-lg border border-sky-200/50 backdrop-blur-sm"
                    title="Edit content"
                  >
                    <Edit className="w-4 h-4" />
                  </motion.button>
                )}
              </>
            )}

            <motion.button
              onClick={handleCopy}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-white/30 text-sky-700 rounded-xl hover:bg-white/40 transition-all shadow-lg border border-sky-200/50 backdrop-blur-sm"
              title="Copy content"
            >
              {copied ? <CheckCircle className="w-4 h-4 text-emerald-600" /> : <Copy className="w-4 h-4" />}
            </motion.button>

            <motion.button
              onClick={handleDownload}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-white/30 text-sky-700 rounded-xl hover:bg-white/40 transition-all shadow-lg border border-sky-200/50 backdrop-blur-sm"
              title="Download as markdown"
            >
              <Download className="w-4 h-4" />
            </motion.button>

            {showLibraryLink && (
              <Link href="/content?type=blog">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-2 bg-white/30 text-sky-700 rounded-xl hover:bg-white/40 transition-all shadow-lg border border-sky-200/50 backdrop-blur-sm"
                  title="View content library"
                >
                  <Library className="w-4 h-4" />
                </motion.button>
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="sky-card p-8 shadow-2xl"
        >
          {isEditing ? (
            <textarea
              value={editedContent}
              onChange={(e) => setEditedContent(e.target.value)}
              className="w-full h-96 p-4 bg-white/50 backdrop-blur-sm border border-sky-200/50 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-sky-500/50 sky-text font-mono text-sm"
              placeholder="Write your blog content in Markdown..."
            />
          ) : (
            <div className="prose prose-lg max-w-none sky-text prose-headings:sky-heading prose-links:sky-accent prose-strong:sky-heading">
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={customRenderers}
              >
                {content}
              </ReactMarkdown>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
