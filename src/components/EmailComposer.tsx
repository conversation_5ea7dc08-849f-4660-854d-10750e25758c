'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  X, 
  Send, 
  Calendar, 
  Save, 
  Wand2, 
  Users, 
  Clock,
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react'

export interface EmailComposerProps {
  isOpen: boolean
  onClose: () => void
  initialData?: {
    to?: string
    cc?: string
    bcc?: string
    subject?: string
    body?: string
    isHtml?: boolean
  }
  onSent?: (result: { success: boolean; messageId?: string; error?: string }) => void
  onScheduled?: (result: { success: boolean; scheduleId?: string; error?: string }) => void
  onDraftSaved?: (result: { success: boolean; draftId?: string; error?: string }) => void
  showAIAssist?: boolean
  gmailIntegration?: boolean
}

export default function EmailComposer({
  isOpen,
  onClose,
  initialData,
  onSent,
  onScheduled,
  onDraftSaved,
  showAIAssist = true,
  gmailIntegration = true
}: EmailComposerProps) {
  const [formData, setFormData] = useState({
    to: '',
    cc: '',
    bcc: '',
    subject: '',
    body: '',
    isHtml: false,
    scheduleTime: ''
  })

  const [status, setStatus] = useState<'compose' | 'sending' | 'scheduling' | 'saving'>('compose')
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [showSchedule, setShowSchedule] = useState(false)
  const [showCcBcc, setShowCcBcc] = useState(false)
  const [aiAssisting, setAiAssisting] = useState(false)

  // Initialize form with provided data
  useEffect(() => {
    if (isOpen && initialData) {
      setFormData({
        to: initialData.to || '',
        cc: initialData.cc || '',
        bcc: initialData.bcc || '',
        subject: initialData.subject || '',
        body: initialData.body || '',
        isHtml: initialData.isHtml || false,
        scheduleTime: ''
      })
      
      if (initialData.cc || initialData.bcc) {
        setShowCcBcc(true)
      }
    }
  }, [isOpen, initialData])

  // Validation
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}
    
    if (!formData.to.trim()) {
      newErrors.to = 'At least one recipient is required'
    } else {
      // Basic email validation
      const emails = formData.to.split(',').map(email => email.trim())
      const invalidEmails = emails.filter(email => !isValidEmail(email))
      if (invalidEmails.length > 0) {
        newErrors.to = `Invalid email addresses: ${invalidEmails.join(', ')}`
      }
    }
    
    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required'
    }
    
    if (!formData.body.trim()) {
      newErrors.body = 'Message body is required'
    }

    if (showSchedule && formData.scheduleTime) {
      const scheduleDate = new Date(formData.scheduleTime)
      if (scheduleDate <= new Date()) {
        newErrors.scheduleTime = 'Schedule time must be in the future'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const handleSend = async () => {
    if (!validateForm()) return

    setStatus('sending')
    
    try {
      const endpoint = gmailIntegration ? '/api/gmail/send' : '/api/generate/email'
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: formData.to,
          cc: formData.cc || undefined,
          bcc: formData.bcc || undefined,
          subject: formData.subject,
          body: formData.body,
          isHtml: formData.isHtml
        })
      })

      const data = await response.json()
      
      if (data.success) {
        onSent?.({ success: true, messageId: data.messageId })
        resetForm()
        onClose()
      } else {
        onSent?.({ success: false, error: data.error })
        setErrors({ general: data.error })
      }
    } catch (error) {
      onSent?.({ success: false, error: 'Failed to send email' })
      setErrors({ general: 'Failed to send email' })
    } finally {
      setStatus('compose')
    }
  }

  const handleSchedule = async () => {
    if (!validateForm()) return
    if (!formData.scheduleTime) {
      setErrors({ scheduleTime: 'Schedule time is required' })
      return
    }

    setStatus('scheduling')
    
    try {
      const response = await fetch('/api/gmail/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: formData.to,
          cc: formData.cc || undefined,
          bcc: formData.bcc || undefined,
          subject: formData.subject,
          body: formData.body,
          isHtml: formData.isHtml,
          scheduleTime: formData.scheduleTime
        })
      })

      const data = await response.json()
      
      if (data.success) {
        onScheduled?.({ success: true, scheduleId: data.scheduleId })
        resetForm()
        onClose()
      } else {
        onScheduled?.({ success: false, error: data.error })
        setErrors({ general: data.error })
      }
    } catch (error) {
      onScheduled?.({ success: false, error: 'Failed to schedule email' })
      setErrors({ general: 'Failed to schedule email' })
    } finally {
      setStatus('compose')
    }
  }

  const handleSaveDraft = async () => {
    if (!formData.subject.trim() && !formData.body.trim()) {
      setErrors({ general: 'Nothing to save as draft' })
      return
    }

    setStatus('saving')
    
    try {
      // This would typically save to Gmail drafts or local storage
      // For now, we'll simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onDraftSaved?.({ success: true, draftId: `draft_${Date.now()}` })
      setErrors({ general: 'Draft saved successfully' })
    } catch (error) {
      onDraftSaved?.({ success: false, error: 'Failed to save draft' })
      setErrors({ general: 'Failed to save draft' })
    } finally {
      setStatus('compose')
    }
  }

  const handleAIAssist = async () => {
    if (!formData.subject.trim()) {
      setErrors({ subject: 'Please provide a subject to get AI assistance' })
      return
    }

    setAiAssisting(true)
    
    try {
      const response = await fetch('/api/email-generator/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          purpose: formData.subject,
          audience: 'General audience',
          tone: 'professional',
          keyPoints: [],
          includeSubject: false
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setFormData(prev => ({ ...prev, body: data.content }))
      } else {
        setErrors({ general: 'Failed to get AI assistance' })
      }
    } catch (error) {
      setErrors({ general: 'Failed to get AI assistance' })
    } finally {
      setAiAssisting(false)
    }
  }

  const resetForm = () => {
    setFormData({
      to: '',
      cc: '',
      bcc: '',
      subject: '',
      body: '',
      isHtml: false,
      scheduleTime: ''
    })
    setErrors({})
    setShowSchedule(false)
    setShowCcBcc(false)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600">
              <Send className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">Compose Email</h3>
              <p className="text-gray-400 text-sm">
                {gmailIntegration ? 'Send via Gmail' : 'Generate & Send'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-6">
            {/* Error Display */}
            {errors.general && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className={`p-4 rounded-xl border flex items-center space-x-3 ${
                  errors.general.includes('success') 
                    ? 'bg-green-500/10 border-green-500/20 text-green-400'
                    : 'bg-red-500/10 border-red-500/20 text-red-400'
                }`}
              >
                {errors.general.includes('success') ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <AlertCircle className="w-5 h-5" />
                )}
                <span>{errors.general}</span>
              </motion.div>
            )}

            {/* Recipients */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">To *</label>
                <input
                  type="text"
                  value={formData.to}
                  onChange={(e) => setFormData({...formData, to: e.target.value})}
                  placeholder="<EMAIL>, <EMAIL>"
                  className={`w-full px-4 py-3 bg-white/10 border rounded-xl text-white placeholder-gray-400 focus:bg-white/20 transition-all ${
                    errors.to ? 'border-red-500/50' : 'border-white/20 focus:border-blue-500/50'
                  }`}
                />
                {errors.to && (
                  <p className="text-red-400 text-sm mt-1">{errors.to}</p>
                )}
              </div>

              {/* Show/Hide CC/BCC */}
              {!showCcBcc && (
                <button
                  onClick={() => setShowCcBcc(true)}
                  className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
                >
                  + Add CC/BCC
                </button>
              )}

              {showCcBcc && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">CC</label>
                    <input
                      type="text"
                      value={formData.cc}
                      onChange={(e) => setFormData({...formData, cc: e.target.value})}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50 transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">BCC</label>
                    <input
                      type="text"
                      value={formData.bcc}
                      onChange={(e) => setFormData({...formData, bcc: e.target.value})}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-blue-500/50 transition-all"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Subject *</label>
              <div className="relative">
                <input
                  type="text"
                  value={formData.subject}
                  onChange={(e) => setFormData({...formData, subject: e.target.value})}
                  placeholder="Email subject"
                  className={`w-full px-4 py-3 bg-white/10 border rounded-xl text-white placeholder-gray-400 focus:bg-white/20 transition-all ${
                    errors.subject ? 'border-red-500/50' : 'border-white/20 focus:border-blue-500/50'
                  }`}
                />
                {showAIAssist && (
                  <button
                    onClick={handleAIAssist}
                    disabled={aiAssisting || !formData.subject.trim()}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-purple-400 hover:text-purple-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="AI Assist"
                  >
                    {aiAssisting ? (
                      <div className="animate-spin w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full" />
                    ) : (
                      <Wand2 className="w-4 h-4" />
                    )}
                  </button>
                )}
              </div>
              {errors.subject && (
                <p className="text-red-400 text-sm mt-1">{errors.subject}</p>
              )}
            </div>

            {/* Message Body */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Message *</label>
              <textarea
                value={formData.body}
                onChange={(e) => setFormData({...formData, body: e.target.value})}
                placeholder="Write your email message here..."
                rows={12}
                className={`w-full px-4 py-3 bg-white/10 border rounded-xl text-white placeholder-gray-400 focus:bg-white/20 transition-all resize-none ${
                  errors.body ? 'border-red-500/50' : 'border-white/20 focus:border-blue-500/50'
                }`}
              />
              {errors.body && (
                <p className="text-red-400 text-sm mt-1">{errors.body}</p>
              )}
            </div>

            {/* Schedule Section */}
            {showSchedule && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Schedule Time</label>
                <input
                  type="datetime-local"
                  value={formData.scheduleTime}
                  onChange={(e) => setFormData({...formData, scheduleTime: e.target.value})}
                  min={new Date().toISOString().slice(0, 16)}
                  className={`w-full px-4 py-3 bg-white/10 border rounded-xl text-white focus:bg-white/20 transition-all ${
                    errors.scheduleTime ? 'border-red-500/50' : 'border-white/20 focus:border-blue-500/50'
                  }`}
                />
                {errors.scheduleTime && (
                  <p className="text-red-400 text-sm mt-1">{errors.scheduleTime}</p>
                )}
              </div>
            )}

            {/* Options */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isHtml"
                    checked={formData.isHtml}
                    onChange={(e) => setFormData({...formData, isHtml: e.target.checked})}
                    className="w-4 h-4 text-blue-600 bg-white/10 border-white/20 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="isHtml" className="text-sm text-gray-300">Send as HTML</label>
                </div>
                
                {!showSchedule && (
                  <button
                    onClick={() => setShowSchedule(true)}
                    className="text-blue-400 hover:text-blue-300 text-sm transition-colors flex items-center space-x-1"
                  >
                    <Clock className="w-4 h-4" />
                    <span>Schedule</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="p-6 border-t border-white/10 bg-white/5">
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              <motion.button
                onClick={handleSaveDraft}
                disabled={status === 'saving'}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 text-white rounded-lg transition-all flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {status === 'saving' ? (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>Save Draft</span>
              </motion.button>
            </div>

            <div className="flex space-x-3">
              {showSchedule && formData.scheduleTime && (
                <motion.button
                  onClick={handleSchedule}
                  disabled={status === 'scheduling'}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center space-x-2"
                >
                  {status === 'scheduling' ? (
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                  ) : (
                    <Calendar className="w-4 h-4" />
                  )}
                  <span>Schedule</span>
                </motion.button>
              )}

              <motion.button
                onClick={handleSend}
                disabled={status === 'sending'}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center space-x-2"
              >
                {status === 'sending' ? (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
                <span>Send Now</span>
              </motion.button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}